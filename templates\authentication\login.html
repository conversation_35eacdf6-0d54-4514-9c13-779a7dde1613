<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Cloud Central SS</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% load static %}{% static 'images/GEV-e06a174f.png' %}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{% load static %}{% static 'css/custom.css' %}" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid vh-100">
        <div class="row h-100">
            <!-- Left side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-vernova">
                <div class="text-center text-white">
                    <img src="{% load static %}{% static 'images/ge_vernova.svg' %}" alt="GE Vernova" height="120" class="mb-4" style="filter: brightness(0) invert(1);">
                    <h1 class="display-4 fw-bold mb-3">Cloud Central SS</h1>
                    <p class="lead mb-4">AWS Resource Inventory Management System</p>
                    <div class="row text-center">
                        <div class="col-4">
                            <i class="fas fa-server fa-2x mb-2"></i>
                            <p class="small">Resource<br>Management</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-tasks fa-2x mb-2"></i>
                            <p class="small">Job<br>Scheduling</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <p class="small">Analytics &<br>Reporting</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right side - Login Form -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center">
                <div class="w-100" style="max-width: 400px;">
                    <!-- Mobile logo -->
                    <div class="text-center d-lg-none mb-4">
                        <img src="{% load static %}{% static 'images/GEV-e06a174f.png' %}" alt="GE Vernova" height="60" class="mb-2">
                        <h3 class="text-vernova">Cloud Central SS</h3>
                    </div>
                    
                    <div class="card shadow">
                        <div class="card-header bg-vernova text-white text-center">
                            <h4 class="mb-0">
                                <i class="fas fa-sign-in-alt me-2"></i>Sign In
                            </h4>
                        </div>
                        <div class="card-body p-4">
                            <!-- Messages -->
                            {% if messages %}
                                {% for message in messages %}
                                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                            
                            <!-- Login Form -->
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="mb-3">
                                    <label for="{{ form.username.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-1"></i>Username
                                    </label>
                                    <input type="text" 
                                           class="form-control {% if form.username.errors %}is-invalid{% endif %}" 
                                           id="{{ form.username.id_for_label }}" 
                                           name="{{ form.username.name }}" 
                                           value="{{ form.username.value|default:'' }}"
                                           placeholder="Enter your username"
                                           required>
                                    {% if form.username.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.username.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.password.id_for_label }}" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Password
                                    </label>
                                    <input type="password" 
                                           class="form-control {% if form.password.errors %}is-invalid{% endif %}" 
                                           id="{{ form.password.id_for_label }}" 
                                           name="{{ form.password.name }}"
                                           placeholder="Enter your password"
                                           required>
                                    {% if form.password.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.password.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                {% if form.non_field_errors %}
                                    <div class="alert alert-danger">
                                        {{ form.non_field_errors.0 }}
                                    </div>
                                {% endif %}
                                
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-vernova">
                                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                    </button>
                                </div>
                            </form>
                            
                            <!-- SSO Option -->
                            <div class="text-center">
                                <hr class="my-3">
                                <p class="text-muted small mb-2">Or sign in with</p>
                                <a href="{% url 'authentication:saml_login' %}" class="btn btn-outline-vernova">
                                    <i class="fas fa-key me-2"></i>SSO (SAML)
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Demo Credentials -->
                    <div class="card mt-3 border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Demo Credentials
                            </h6>
                        </div>
                        <div class="card-body p-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <strong>Admin</strong><br>
                                    <small>admin / admin123</small>
                                </div>
                                <div class="col-4">
                                    <strong>Operations</strong><br>
                                    <small>jane.ops / password123</small>
                                </div>
                                <div class="col-4">
                                    <strong>Read-Only</strong><br>
                                    <small>bob.readonly / password123</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Footer -->
                    <div class="text-center mt-4">
                        <p class="text-muted small">
                            &copy; 2024 GE Vernova. All rights reserved.<br>
                            Cloud Central SS v1.0.0
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
