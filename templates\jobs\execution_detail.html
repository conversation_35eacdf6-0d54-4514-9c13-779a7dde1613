{% extends 'base.html' %}
{% load static %}

{% block title %}Job Execution - Cloud Central SS{% endblock %}

{% block extra_css %}
<style>
    .execution-header {
        background: linear-gradient(135deg, #005E60 0%, #007a7d 100%);
        color: white;
        border-radius: 8px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .status-badge {
        font-size: 1rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
    }
    .status-pending { background: #ffc107; color: #000; }
    .status-running { background: #007bff; color: white; }
    .status-completed { background: #28a745; color: white; }
    .status-failed { background: #dc3545; color: white; }
    .status-cancelled { background: #6c757d; color: white; }
    
    .progress-section {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .metric-card {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 1rem;
        text-align: center;
        margin-bottom: 1rem;
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #005E60;
    }
    .task-item {
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        background: white;
    }
    .task-status {
        font-size: 0.875rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Execution Header -->
            <div class="execution-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h2><i class="fas fa-play-circle me-2"></i>{{ execution.job.name }}</h2>
                        <p class="mb-2">Execution ID: {{ execution.id }}</p>
                        <p class="mb-0">Started by {{ execution.started_by.username }} on {{ execution.created_at|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="text-end">
                        <span class="status-badge status-{{ execution.status }}">
                            {% if execution.status == 'pending' %}
                                <i class="fas fa-clock me-1"></i>Pending
                            {% elif execution.status == 'running' %}
                                <i class="fas fa-spinner fa-spin me-1"></i>Running
                            {% elif execution.status == 'completed' %}
                                <i class="fas fa-check me-1"></i>Completed
                            {% elif execution.status == 'failed' %}
                                <i class="fas fa-times me-1"></i>Failed
                            {% elif execution.status == 'cancelled' %}
                                <i class="fas fa-stop me-1"></i>Cancelled
                            {% endif %}
                            {{ execution.get_status_display }}
                        </span>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Progress and Metrics -->
                <div class="col-md-8">
                    <!-- Progress Section -->
                    <div class="progress-section">
                        <h5><i class="fas fa-chart-line me-2"></i>Execution Progress</h5>
                        
                        {% if execution.status == 'running' or execution.status == 'completed' %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Progress</span>
                                    <span>{{ execution.progress_percentage|floatformat:1 }}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ execution.progress_percentage }}%"
                                         aria-valuenow="{{ execution.progress_percentage }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        <div class="row">
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value">{{ execution.total_tasks }}</div>
                                    <div class="text-muted">Total Tasks</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value">{{ execution.completed_tasks }}</div>
                                    <div class="text-muted">Completed</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value">{{ execution.resources_collected }}</div>
                                    <div class="text-muted">Resources Found</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value">{{ execution.failed_tasks }}</div>
                                    <div class="text-muted">Failed Tasks</div>
                                </div>
                            </div>
                        </div>

                        {% if execution.duration %}
                            <div class="mt-3">
                                <strong>Duration:</strong> {{ execution.duration }}
                            </div>
                        {% endif %}

                        {% if execution.error_message %}
                            <div class="alert alert-danger mt-3">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Error Details</h6>
                                <p class="mb-0">{{ execution.error_message }}</p>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Task Results -->
                    <div class="progress-section">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="fas fa-tasks me-2"></i>Task Results</h5>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshTasks()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>

                        <div id="taskResults">
                            {% if task_results %}
                                {% for task in task_results %}
                                    <div class="task-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">{{ task.task_name }}</h6>
                                                <small class="text-muted">
                                                    {{ task.aws_account_id }} • {{ task.region }} • {{ task.resource_type }}
                                                </small>
                                            </div>
                                            <span class="task-status status-{{ task.status }}">
                                                {{ task.get_status_display }}
                                            </span>
                                        </div>
                                        
                                        {% if task.status == 'completed' %}
                                            <div class="mt-2">
                                                <small class="text-success">
                                                    <i class="fas fa-check me-1"></i>
                                                    {{ task.items_successful }}/{{ task.items_processed }} items processed successfully
                                                </small>
                                                {% if task.execution_time %}
                                                    <small class="text-muted ms-3">
                                                        <i class="fas fa-clock me-1"></i>{{ task.execution_time|floatformat:2 }}s
                                                    </small>
                                                {% endif %}
                                            </div>
                                        {% elif task.status == 'failed' %}
                                            <div class="mt-2">
                                                <small class="text-danger">
                                                    <i class="fas fa-times me-1"></i>{{ task.error_message }}
                                                </small>
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-tasks fa-2x mb-2"></i>
                                    <p>No task results available yet.</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-md-4">
                    <!-- Job Information -->
                    <div class="progress-section">
                        <h5><i class="fas fa-info-circle me-2"></i>Job Information</h5>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Job Type:</strong></td>
                                <td>{{ execution.job.get_job_type_display }}</td>
                            </tr>
                            <tr>
                                <td><strong>AWS Session:</strong></td>
                                <td>{{ execution.job.aws_session.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Resource Types:</strong></td>
                                <td>
                                    {% for resource_type in execution.job.resource_types %}
                                        <span class="badge bg-secondary me-1">{{ resource_type }}</span>
                                    {% endfor %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Parallel Settings:</strong></td>
                                <td>
                                    Accounts: {{ execution.job.parallel_accounts }}<br>
                                    Regions: {{ execution.job.parallel_regions }}<br>
                                    Resources: {{ execution.job.parallel_resources }}
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Actions -->
                    <div class="progress-section">
                        <h5><i class="fas fa-cogs me-2"></i>Actions</h5>
                        <div class="d-grid gap-2">
                            {% if execution.status == 'running' %}
                                <a href="{% url 'jobs:execution_stop' execution.pk %}" class="btn btn-warning">
                                    <i class="fas fa-stop me-1"></i>Stop Execution
                                </a>
                            {% elif execution.status == 'failed' or execution.status == 'cancelled' %}
                                <a href="{% url 'jobs:execution_retry' execution.pk %}" class="btn btn-primary">
                                    <i class="fas fa-redo me-1"></i>Retry Execution
                                </a>
                            {% endif %}
                            
                            <a href="{% url 'jobs:job_detail' execution.job.pk %}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>View Job Details
                            </a>
                            
                            <a href="{% url 'jobs:execution_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-list me-1"></i>All Executions
                            </a>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    {% if metrics %}
                        <div class="progress-section">
                            <h5><i class="fas fa-chart-bar me-2"></i>Performance Metrics</h5>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Execution Time:</strong></td>
                                    <td>{{ metrics.total_execution_time|floatformat:2 }}s</td>
                                </tr>
                                <tr>
                                    <td><strong>API Calls:</strong></td>
                                    <td>{{ metrics.total_api_calls }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Resources Found:</strong></td>
                                    <td>{{ metrics.total_resources_found }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Workers Used:</strong></td>
                                    <td>{{ metrics.workers_used }}</td>
                                </tr>
                            </table>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh for running executions
{% if execution.status == 'running' or execution.status == 'pending' %}
    setInterval(function() {
        location.reload();
    }, 10000); // Refresh every 10 seconds
{% endif %}

function refreshTasks() {
    // Reload the page to get updated task results
    location.reload();
}

// Add real-time updates if WebSocket is available
// This would be implemented with Django Channels in production
</script>
{% endblock %}
