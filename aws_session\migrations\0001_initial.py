# Generated by Django 4.2.7 on 2025-05-29 15:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AWSSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('auth_method', models.CharField(choices=[('profile', 'Profile-based Authentication'), ('access_key', 'Access Key/Secret Key Authentication'), ('assume_role', 'STS Assume Role Authentication'), ('instance_profile', 'Instance Profile Authentication')], max_length=20)),
                ('profile_name', models.Char<PERSON><PERSON>(blank=True, max_length=100)),
                ('access_key_id', models.Char<PERSON><PERSON>(blank=True, max_length=100)),
                ('secret_access_key', models.Char<PERSON>ield(blank=True, max_length=200)),
                ('role_arn', models.CharField(blank=True, max_length=500)),
                ('external_id', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('session_name', models.CharField(blank=True, max_length=100)),
                ('region', models.CharField(default='us-east-1', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='aws_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'AWS Session',
                'verbose_name_plural': 'AWS Sessions',
            },
        ),
        migrations.CreateModel(
            name='SessionTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('test_type', models.CharField(default='connection', max_length=50)),
                ('is_successful', models.BooleanField(default=False)),
                ('error_message', models.TextField(blank=True)),
                ('response_time', models.FloatField(blank=True, null=True)),
                ('tested_at', models.DateTimeField(auto_now_add=True)),
                ('aws_session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='test_results', to='aws_session.awssession')),
                ('tested_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Session Test',
                'verbose_name_plural': 'Session Tests',
                'indexes': [models.Index(fields=['aws_session', 'tested_at'], name='aws_session_aws_ses_8dd732_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='awssession',
            index=models.Index(fields=['auth_method', 'is_active'], name='aws_session_auth_me_057656_idx'),
        ),
    ]
