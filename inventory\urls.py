from django.urls import path
from . import views

app_name = 'inventory'

urlpatterns = [
    # Dashboard
    path('', views.DashboardView.as_view(), name='dashboard'),
    
    # Resource views
    path('resources/', views.ResourceListView.as_view(), name='resource_list'),
    path('resources/<str:resource_type>/', views.ResourceTypeListView.as_view(), name='resource_type_list'),
    path('resource/<int:pk>/', views.ResourceDetailView.as_view(), name='resource_detail'),
    
    # Search and filter
    path('search/', views.ResourceSearchView.as_view(), name='resource_search'),
    path('filter/', views.ResourceFilterView.as_view(), name='resource_filter'),
    
    # Export
    path('export/', views.ResourceExportView.as_view(), name='resource_export'),
    
    # API endpoints
    path('api/resources/', views.ResourceListAPIView.as_view(), name='api_resource_list'),
    path('api/resources/<int:pk>/', views.ResourceDetailAPIView.as_view(), name='api_resource_detail'),
    path('api/search/', views.ResourceSearchAPIView.as_view(), name='api_resource_search'),
]
