{% extends 'base.html' %}
{% load static %}

{% block title %}{{ resource.resource_name }} - Cloud Central SS{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:resource_list' %}">Resources</a></li>
        <li class="breadcrumb-item active" aria-current="page">{{ resource.resource_name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Resource Header -->
        <div class="card mb-4">
            <div class="card-header card-header-vernova">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-server resource-{{ resource.resource_type|lower }} me-2"></i>
                            {{ resource.resource_name }}
                        </h4>
                        <small class="text-light">{{ resource.resource_type }} Resource</small>
                    </div>
                    <div class="col-auto">
                        <span class="badge badge-{{ resource.state|lower }} fs-6">{{ resource.state|title }}</span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Basic Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Resource ID:</strong></td>
                                <td><code>{{ resource.resource_id }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Resource Type:</strong></td>
                                <td><span class="badge bg-secondary">{{ resource.resource_type }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>State:</strong></td>
                                <td><span class="badge badge-{{ resource.state|lower }}">{{ resource.state|title }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Region:</strong></td>
                                <td>{{ resource.region }}</td>
                            </tr>
                            {% if resource.arn %}
                            <tr>
                                <td><strong>ARN:</strong></td>
                                <td><code class="small">{{ resource.arn }}</code></td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Account Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Account Name:</strong></td>
                                <td>{{ resource.aws_account.account_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Account ID:</strong></td>
                                <td><code>{{ resource.aws_account.account_id }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Business Unit:</strong></td>
                                <td>
                                    <span class="badge bg-vernova">{{ resource.aws_account.business_unit.code }}</span>
                                    {{ resource.aws_account.business_unit.name }}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>
                                    {% if resource.created_date %}
                                        {{ resource.created_date|date:"M d, Y H:i" }}
                                    {% else %}
                                        <em>Unknown</em>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Last Updated:</strong></td>
                                <td>{{ resource.last_updated|date:"M d, Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resource-Specific Details -->
{% if resource.resource_type == 'EC2' %}
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header card-header-vernova">
                <h6 class="card-title mb-0">
                    <i class="fas fa-desktop me-2"></i>EC2 Instance Details
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Instance Type:</strong></td>
                        <td><code>{{ resource.ec2instance.instance_type }}</code></td>
                    </tr>
                    <tr>
                        <td><strong>Platform:</strong></td>
                        <td>{{ resource.ec2instance.platform|default:"Unknown" }}</td>
                    </tr>
                    {% if resource.ec2instance.vcpu_count %}
                    <tr>
                        <td><strong>vCPUs:</strong></td>
                        <td>{{ resource.ec2instance.vcpu_count }}</td>
                    </tr>
                    {% endif %}
                    {% if resource.ec2instance.memory_size %}
                    <tr>
                        <td><strong>Memory:</strong></td>
                        <td>{{ resource.ec2instance.memory_size }} MB</td>
                    </tr>
                    {% endif %}
                    {% if resource.ec2instance.public_ip %}
                    <tr>
                        <td><strong>Public IP:</strong></td>
                        <td><code>{{ resource.ec2instance.public_ip }}</code></td>
                    </tr>
                    {% endif %}
                    {% if resource.ec2instance.private_ip %}
                    <tr>
                        <td><strong>Private IP:</strong></td>
                        <td><code>{{ resource.ec2instance.private_ip }}</code></td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
    
    {% if resource.ec2instance.vpc %}
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header card-header-vernova">
                <h6 class="card-title mb-0">
                    <i class="fas fa-network-wired me-2"></i>Network Information
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>VPC:</strong></td>
                        <td><code>{{ resource.ec2instance.vpc.vpc_id }}</code></td>
                    </tr>
                    <tr>
                        <td><strong>CIDR Block:</strong></td>
                        <td>{{ resource.ec2instance.vpc.cidr_block }}</td>
                    </tr>
                    {% if resource.ec2instance.subnet %}
                    <tr>
                        <td><strong>Subnet:</strong></td>
                        <td><code>{{ resource.ec2instance.subnet.subnet_id }}</code></td>
                    </tr>
                    <tr>
                        <td><strong>Availability Zone:</strong></td>
                        <td>{{ resource.ec2instance.subnet.availability_zone }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Tags -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header card-header-vernova">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tags me-2"></i>Resource Tags
                </h6>
            </div>
            <div class="card-body">
                {% if resource.tags.all %}
                <div class="row">
                    {% for tag in resource.tags.all %}
                    <div class="col-lg-4 col-md-6 mb-2">
                        <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                            <strong>{{ tag.key }}:</strong>
                            <span class="badge bg-vernova">{{ tag.value|default:"(empty)" }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted mb-0">
                    <i class="fas fa-info-circle me-2"></i>No tags found for this resource.
                </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-vernova">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="btn-group me-2">
                    <a href="{% url 'inventory:resource_list' %}" class="btn btn-outline-vernova">
                        <i class="fas fa-arrow-left me-2"></i>Back to Resources
                    </a>
                </div>
                
                {% if user.profile.is_admin or user.profile.is_operation_support %}
                <div class="btn-group me-2">
                    <button class="btn btn-outline-vernova" onclick="refreshResource()">
                        <i class="fas fa-sync me-2"></i>Refresh Data
                    </button>
                </div>
                {% endif %}
                
                <div class="btn-group">
                    <button class="btn btn-outline-vernova" onclick="exportResource('json')">
                        <i class="fas fa-download me-2"></i>Export JSON
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshResource() {
    showNotification('Resource refresh initiated...', 'info');
    // Implement refresh functionality
}

function exportResource(format) {
    const data = {
        id: {{ resource.id }},
        resource_name: '{{ resource.resource_name }}',
        resource_type: '{{ resource.resource_type }}',
        resource_id: '{{ resource.resource_id }}',
        state: '{{ resource.state }}',
        region: '{{ resource.region }}',
        arn: '{{ resource.arn }}',
        account: {
            name: '{{ resource.aws_account.account_name }}',
            id: '{{ resource.aws_account.account_id }}',
            business_unit: '{{ resource.aws_account.business_unit.name }}'
        },
        tags: [
            {% for tag in resource.tags.all %}
            {key: '{{ tag.key }}', value: '{{ tag.value }}'}{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        last_updated: '{{ resource.last_updated|date:"c" }}'
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '{{ resource.resource_name }}.json';
    a.click();
    URL.revokeObjectURL(url);
}
</script>
{% endblock %}
