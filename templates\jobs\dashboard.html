{% extends 'base.html' %}
{% load static %}

{% block title %}Jobs Dashboard - Cloud Central SS{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item active" aria-current="page">Jobs</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 bg-vernova text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="card-title mb-1">
                            <i class="fas fa-tasks me-2"></i>Jobs Dashboard
                        </h2>
                        <p class="card-text mb-0">
                            Manage and monitor AWS resource collection jobs
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        {% if user.profile.is_admin %}
                        <a href="{% url 'jobs:job_create' %}" class="btn btn-light">
                            <i class="fas fa-plus me-2"></i>Create New Job
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card dashboard-card h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="card-title text-muted mb-0">Total Jobs</h6>
                    <i class="fas fa-briefcase text-vernova fa-2x"></i>
                </div>
                <div class="dashboard-stat">0</div>
                <small class="text-muted">Configured jobs</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card dashboard-card h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="card-title text-muted mb-0">Running Jobs</h6>
                    <i class="fas fa-play-circle text-info fa-2x"></i>
                </div>
                <div class="dashboard-stat">0</div>
                <small class="text-muted">Currently executing</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card dashboard-card h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="card-title text-muted mb-0">Completed Today</h6>
                    <i class="fas fa-check-circle text-success fa-2x"></i>
                </div>
                <div class="dashboard-stat">0</div>
                <small class="text-muted">Successful executions</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card dashboard-card h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="card-title text-muted mb-0">Failed Jobs</h6>
                    <i class="fas fa-exclamation-circle text-danger fa-2x"></i>
                </div>
                <div class="dashboard-stat">0</div>
                <small class="text-muted">Require attention</small>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-vernova">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if user.profile.is_admin %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'jobs:job_create' %}" class="btn btn-outline-vernova w-100">
                            <i class="fas fa-plus me-2"></i>Create Job
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'jobs:schedule_list' %}" class="btn btn-outline-vernova w-100">
                            <i class="fas fa-calendar me-2"></i>Manage Schedules
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'jobs:execution_list' %}" class="btn btn-outline-vernova w-100">
                            <i class="fas fa-history me-2"></i>View Executions
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'jobs:worker_status' %}" class="btn btn-outline-vernova w-100">
                            <i class="fas fa-server me-2"></i>Worker Status
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Job Executions -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header card-header-vernova">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Job Executions
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center text-muted py-4">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <p>No job executions found.</p>
                    {% if user.profile.is_admin %}
                    <a href="{% url 'jobs:job_create' %}" class="btn btn-outline-vernova">
                        Create Your First Job
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Status -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header card-header-vernova">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat me-2"></i>System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Celery Workers</span>
                        <span class="badge bg-warning">Not Configured</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Redis Broker</span>
                        <span class="badge bg-warning">Not Configured</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Job Queue</span>
                        <span class="badge bg-secondary">Empty</span>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">
                        Configure Celery and Redis to enable job processing
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Setup Instructions -->
<div class="row">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Setup Required
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-3">To enable job processing, you need to configure the following components:</p>
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-database me-2"></i>Redis Server</h6>
                        <p class="small text-muted">Install and start Redis server for message brokering</p>
                        <code class="small">redis-server</code>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-cogs me-2"></i>Celery Worker</h6>
                        <p class="small text-muted">Start Celery worker processes</p>
                        <code class="small">celery -A cloud_inventory worker -l info</code>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-clock me-2"></i>Celery Beat</h6>
                        <p class="small text-muted">Start Celery beat for scheduled jobs</p>
                        <code class="small">celery -A cloud_inventory beat -l info</code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-refresh dashboard every 30 seconds
    setInterval(function() {
        // Refresh job statistics
        refreshJobStats();
    }, 30000);
});

function refreshJobStats() {
    // Placeholder for job statistics refresh
    console.log('Refreshing job statistics...');
}
</script>
{% endblock %}
