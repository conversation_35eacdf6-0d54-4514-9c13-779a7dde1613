{% extends 'base.html' %}
{% load static %}

{% block title %}Resources - Cloud Central SS{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item active" aria-current="page">Resources</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Filters Sidebar -->
    <div class="col-lg-3 mb-4">
        <div class="card">
            <div class="card-header card-header-vernova">
                <h6 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>Filters
                </h6>
            </div>
            <div class="card-body">
                <form method="get" id="filter-form">
                    <!-- Search -->
                    <div class="mb-3">
                        <label class="form-label">Search</label>
                        <div class="search-box">
                            <input type="text" class="form-control" name="search" 
                                   value="{{ current_filters.search }}" 
                                   placeholder="Search resources..." id="resource-search">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    
                    <!-- Resource Type -->
                    <div class="mb-3">
                        <label class="form-label">Resource Type</label>
                        <select class="form-select" name="type">
                            <option value="">All Types</option>
                            {% for type_code, type_name in resource_types %}
                            <option value="{{ type_code }}" {% if current_filters.type == type_code %}selected{% endif %}>
                                {{ type_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- AWS Account -->
                    <div class="mb-3">
                        <label class="form-label">AWS Account</label>
                        <select class="form-select" name="account">
                            <option value="">All Accounts</option>
                            {% for account in aws_accounts %}
                            <option value="{{ account.id }}" {% if current_filters.account == account.id|stringformat:"s" %}selected{% endif %}>
                                {{ account.account_name }} ({{ account.business_unit.code }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Region -->
                    <div class="mb-3">
                        <label class="form-label">Region</label>
                        <select class="form-select" name="region">
                            <option value="">All Regions</option>
                            {% for region in regions %}
                            <option value="{{ region }}" {% if current_filters.region == region %}selected{% endif %}>
                                {{ region }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- State -->
                    <div class="mb-3">
                        <label class="form-label">State</label>
                        <select class="form-select" name="state">
                            <option value="">All States</option>
                            {% for state in states %}
                            <option value="{{ state }}" {% if current_filters.state == state %}selected{% endif %}>
                                {{ state|title }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-vernova">
                            <i class="fas fa-search me-2"></i>Apply Filters
                        </button>
                        <a href="{% url 'inventory:resource_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Resources List -->
    <div class="col-lg-9">
        <div class="card">
            <div class="card-header card-header-vernova d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>AWS Resources
                </h5>
                <div class="btn-group">
                    <a href="{% url 'inventory:resource_export' %}?format=csv" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-download me-1"></i>CSV
                    </a>
                    <a href="{% url 'inventory:resource_export' %}?format=json" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-download me-1"></i>JSON
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Resource Count -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span id="resource-count" class="text-muted">
                        Showing {{ resources|length }} of {{ paginator.count }} resources
                    </span>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary" onclick="toggleView('table')">
                            <i class="fas fa-table"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="toggleView('cards')">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Resources Table -->
                <div class="table-responsive" id="table-view">
                    <table class="table table-hover">
                        <thead class="table-vernova">
                            <tr>
                                <th>Resource</th>
                                <th>Type</th>
                                <th>Account</th>
                                <th>Region</th>
                                <th>State</th>
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for resource in resources %}
                            <tr class="resource-row" 
                                data-resource-type="{{ resource.resource_type }}"
                                data-account="{{ resource.aws_account.id }}"
                                data-region="{{ resource.region }}"
                                data-state="{{ resource.state }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-server resource-{{ resource.resource_type|lower }} me-2"></i>
                                        <div>
                                            <strong class="resource-name">{{ resource.resource_name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ resource.resource_id }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary resource-type">{{ resource.resource_type }}</span>
                                </td>
                                <td>
                                    <div class="account-name">{{ resource.aws_account.account_name }}</div>
                                    <small class="text-muted">{{ resource.aws_account.business_unit.code }}</small>
                                </td>
                                <td>{{ resource.region }}</td>
                                <td>
                                    <span class="badge badge-{{ resource.state|lower }}">{{ resource.state|title }}</span>
                                </td>
                                <td>
                                    <small>{{ resource.last_updated|timesince }} ago</small>
                                </td>
                                <td>
                                    <a href="{% url 'inventory:resource_detail' resource.pk %}" 
                                       class="btn btn-outline-vernova btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center text-muted py-4">
                                    <i class="fas fa-search fa-2x mb-2"></i>
                                    <p>No resources found matching your criteria.</p>
                                    <a href="{% url 'inventory:resource_list' %}" class="btn btn-outline-vernova">
                                        Clear Filters
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Resource pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.account %}&account={{ request.GET.account }}{% endif %}{% if request.GET.region %}&region={{ request.GET.region }}{% endif %}{% if request.GET.state %}&state={{ request.GET.state }}{% endif %}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.account %}&account={{ request.GET.account }}{% endif %}{% if request.GET.region %}&region={{ request.GET.region }}{% endif %}{% if request.GET.state %}&state={{ request.GET.state }}{% endif %}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.account %}&account={{ request.GET.account }}{% endif %}{% if request.GET.region %}&region={{ request.GET.region }}{% endif %}{% if request.GET.state %}&state={{ request.GET.state }}{% endif %}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.account %}&account={{ request.GET.account }}{% endif %}{% if request.GET.region %}&region={{ request.GET.region }}{% endif %}{% if request.GET.state %}&state={{ request.GET.state }}{% endif %}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-submit form on filter change
    $('#filter-form select').on('change', function() {
        $('#filter-form').submit();
    });
    
    // Search with delay
    let searchTimeout;
    $('#resource-search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            $('#filter-form').submit();
        }, 500);
    });
});

function toggleView(viewType) {
    // Placeholder for view toggle functionality
    console.log('Toggle view to:', viewType);
}
</script>
{% endblock %}
