from django.urls import path
from . import views

app_name = 'jobs'

urlpatterns = [
    # Jobs dashboard
    path('', views.JobsDashboardView.as_view(), name='dashboard'),
    
    # Job management
    path('jobs/', views.JobListView.as_view(), name='job_list'),
    path('jobs/create/', views.JobCreateView.as_view(), name='job_create'),
    path('jobs/<uuid:pk>/', views.JobDetailView.as_view(), name='job_detail'),
    path('jobs/<uuid:pk>/edit/', views.JobEditView.as_view(), name='job_edit'),
    path('jobs/<uuid:pk>/delete/', views.JobDeleteView.as_view(), name='job_delete'),
    path('jobs/<uuid:pk>/run/', views.JobRunView.as_view(), name='job_run'),
    
    # Job executions
    path('executions/', views.JobExecutionListView.as_view(), name='execution_list'),
    path('executions/<uuid:pk>/', views.JobExecutionDetailView.as_view(), name='execution_detail'),
    path('executions/<uuid:pk>/stop/', views.JobExecutionStopView.as_view(), name='execution_stop'),
    path('executions/<uuid:pk>/retry/', views.JobExecutionRetryView.as_view(), name='execution_retry'),
    
    # Job scheduling
    path('schedule/', views.JobScheduleListView.as_view(), name='schedule_list'),
    path('schedule/<int:pk>/', views.JobScheduleDetailView.as_view(), name='schedule_detail'),
    
    # Monitoring and metrics
    path('metrics/', views.JobMetricsView.as_view(), name='metrics'),
    path('workers/', views.WorkerStatusView.as_view(), name='worker_status'),
    path('queue/', views.JobQueueView.as_view(), name='job_queue'),
    
    # API endpoints
    path('api/jobs/', views.JobListAPIView.as_view(), name='api_job_list'),
    path('api/jobs/<uuid:pk>/run/', views.JobRunAPIView.as_view(), name='api_job_run'),
    path('api/executions/', views.JobExecutionListAPIView.as_view(), name='api_execution_list'),
    path('api/executions/<uuid:pk>/status/', views.JobExecutionStatusAPIView.as_view(), name='api_execution_status'),
    path('api/metrics/', views.JobMetricsAPIView.as_view(), name='api_metrics'),
    path('api/workers/', views.WorkerStatusAPIView.as_view(), name='api_worker_status'),
]
