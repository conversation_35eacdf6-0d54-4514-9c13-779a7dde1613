{% extends 'base.html' %}
{% load static %}

{% block title %}Create Job - Cloud Central SS{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .form-section h5 {
        color: #005E60;
        margin-bottom: 1rem;
    }
    .resource-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    .resource-type-card {
        border: 2px solid #dee2e6;
        border-radius: 6px;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
    }
    .resource-type-card:hover {
        border-color: #005E60;
        background: #f8f9fa;
    }
    .resource-type-card.selected {
        border-color: #005E60;
        background: #005E60;
        color: white;
    }
    .resource-type-card input[type="checkbox"] {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-plus me-2"></i>Create New Job</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'jobs:dashboard' %}">Jobs</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'jobs:job_list' %}">Job List</a></li>
                        <li class="breadcrumb-item active">Create Job</li>
                    </ol>
                </nav>
            </div>

            <form method="post" id="jobForm">
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle me-2"></i>Basic Information</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_name" class="form-label">Job Name *</label>
                                <input type="text" class="form-control" id="id_name" name="name" required>
                                <div class="form-text">A descriptive name for this job</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_job_type" class="form-label">Job Type *</label>
                                <select class="form-select" id="id_job_type" name="job_type" required>
                                    <option value="">Select job type...</option>
                                    <option value="full_collection">Full Resource Collection</option>
                                    <option value="incremental_collection">Incremental Collection</option>
                                    <option value="specific_resource">Specific Resource Type</option>
                                    <option value="account_specific">Account Specific Collection</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="id_description" class="form-label">Description</label>
                        <textarea class="form-control" id="id_description" name="description" rows="3"></textarea>
                    </div>
                </div>

                <!-- AWS Configuration -->
                <div class="form-section">
                    <h5><i class="fas fa-aws me-2"></i>AWS Configuration</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_aws_session" class="form-label">AWS Session *</label>
                                <select class="form-select" id="id_aws_session" name="aws_session" required>
                                    <option value="">Select AWS session...</option>
                                    {% for session in aws_sessions %}
                                        <option value="{{ session.id }}">{{ session.name }} ({{ session.auth_method }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resource Types -->
                <div class="form-section">
                    <h5><i class="fas fa-cubes me-2"></i>Resource Types</h5>
                    <p class="text-muted">Select the AWS resource types to collect:</p>
                    
                    <div class="resource-type-grid">
                        {% for type_code, type_name in resource_types %}
                            <div class="resource-type-card" onclick="toggleResourceType('{{ type_code }}')">
                                <input type="checkbox" name="resource_types" value="{{ type_code }}" id="resource_{{ type_code }}">
                                <i class="fab fa-aws fa-2x mb-2"></i>
                                <h6>{{ type_name }}</h6>
                            </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Performance Settings -->
                <div class="form-section">
                    <h5><i class="fas fa-tachometer-alt me-2"></i>Performance Settings</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="id_parallel_accounts" class="form-label">Parallel Accounts</label>
                                <input type="number" class="form-control" id="id_parallel_accounts" name="parallel_accounts" value="3" min="1" max="10">
                                <div class="form-text">Number of accounts to process in parallel</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="id_parallel_regions" class="form-label">Parallel Regions</label>
                                <input type="number" class="form-control" id="id_parallel_regions" name="parallel_regions" value="2" min="1" max="5">
                                <div class="form-text">Number of regions to process in parallel</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="id_parallel_resources" class="form-label">Parallel Resources</label>
                                <input type="number" class="form-control" id="id_parallel_resources" name="parallel_resources" value="5" min="1" max="20">
                                <div class="form-text">Number of resource types to process in parallel</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="id_timeout_minutes" class="form-label">Timeout (minutes)</label>
                                <input type="number" class="form-control" id="id_timeout_minutes" name="timeout_minutes" value="60" min="5" max="480">
                                <div class="form-text">Maximum execution time</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="id_retry_count" class="form-label">Retry Count</label>
                                <input type="number" class="form-control" id="id_retry_count" name="retry_count" value="3" min="0" max="10">
                                <div class="form-text">Number of retries on failure</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <a href="{% url 'jobs:job_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Cancel
                    </a>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Create Job
                        </button>
                        <button type="button" class="btn btn-success" onclick="createAndRun()">
                            <i class="fas fa-play me-1"></i>Create & Run
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleResourceType(typeCode) {
    const checkbox = document.getElementById(`resource_${typeCode}`);
    const card = checkbox.closest('.resource-type-card');
    
    checkbox.checked = !checkbox.checked;
    
    if (checkbox.checked) {
        card.classList.add('selected');
    } else {
        card.classList.remove('selected');
    }
}

function createAndRun() {
    // Add a hidden field to indicate we want to run immediately
    const form = document.getElementById('jobForm');
    const runInput = document.createElement('input');
    runInput.type = 'hidden';
    runInput.name = 'run_immediately';
    runInput.value = 'true';
    form.appendChild(runInput);
    
    form.submit();
}

// Form validation
document.getElementById('jobForm').addEventListener('submit', function(e) {
    const resourceTypes = document.querySelectorAll('input[name="resource_types"]:checked');
    
    if (resourceTypes.length === 0) {
        e.preventDefault();
        alert('Please select at least one resource type.');
        return false;
    }
    
    // Convert selected resource types to JSON array
    const selectedTypes = Array.from(resourceTypes).map(cb => cb.value);
    
    // Create hidden input with JSON array
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.name = 'resource_types_json';
    hiddenInput.value = JSON.stringify(selectedTypes);
    this.appendChild(hiddenInput);
});

// Job type change handler
document.getElementById('id_job_type').addEventListener('change', function() {
    const jobType = this.value;
    const resourceSection = document.querySelector('.form-section:nth-child(4)');
    
    if (jobType === 'specific_resource') {
        resourceSection.style.display = 'block';
        // Make resource type selection required
        document.querySelectorAll('input[name="resource_types"]').forEach(cb => {
            cb.required = true;
        });
    } else if (jobType === 'full_collection') {
        // Auto-select all resource types
        document.querySelectorAll('input[name="resource_types"]').forEach(cb => {
            cb.checked = true;
            cb.closest('.resource-type-card').classList.add('selected');
        });
    }
});

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    // Add any initialization code here
});
</script>
{% endblock %}
