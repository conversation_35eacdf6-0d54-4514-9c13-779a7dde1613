from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import random

from inventory.models import (
    BusinessUnit, AWSAccount, BaseResource, EC2Instance, S3Bucket, 
    EKSCluster, ECSCluster, ECRRepository, LambdaFunction, Tag, VPC, SecurityGroup
)
from authentication.models import UserGroup, UserProfile, Permission


class Command(BaseCommand):
    help = 'Populate the database with sample data for development'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before populating',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing data...')
            self.clear_data()

        self.stdout.write('Creating sample data...')
        
        # Create user groups and permissions
        self.create_user_groups()
        
        # Create business units
        self.create_business_units()
        
        # Create AWS accounts
        self.create_aws_accounts()
        
        # Create sample resources
        self.create_sample_resources()
        
        # Create sample users
        self.create_sample_users()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully populated sample data!')
        )

    def clear_data(self):
        """Clear existing data"""
        BaseResource.objects.all().delete()
        AWSAccount.objects.all().delete()
        BusinessUnit.objects.all().delete()
        UserProfile.objects.all().delete()
        UserGroup.objects.all().delete()
        Permission.objects.all().delete()

    def create_user_groups(self):
        """Create user groups and permissions"""
        self.stdout.write('Creating user groups and permissions...')
        
        # Create permissions
        permissions_data = [
            ('view_resources', 'View Resources'),
            ('add_resources', 'Add Resources'),
            ('edit_resources', 'Edit Resources'),
            ('delete_resources', 'Delete Resources'),
            ('manage_aws_sessions', 'Manage AWS Sessions'),
            ('user_management', 'User Management'),
            ('system_configuration', 'System Configuration'),
            ('data_collection', 'Data Collection'),
            ('job_management', 'Job Management'),
            ('export_data', 'Export Data'),
        ]
        
        permissions = {}
        for perm_name, perm_desc in permissions_data:
            permission, created = Permission.objects.get_or_create(
                name=perm_name,
                defaults={'description': perm_desc}
            )
            permissions[perm_name] = permission
        
        # Create user groups
        admin_group, created = UserGroup.objects.get_or_create(
            name='Admin_Group',
            defaults={'description': 'Full system access'}
        )
        admin_group.permissions.set(permissions.values())
        
        ops_group, created = UserGroup.objects.get_or_create(
            name='Operation_Support_Group',
            defaults={'description': 'Limited operational access'}
        )
        ops_group.permissions.set([
            permissions['view_resources'],
            permissions['export_data'],
            permissions['data_collection'],
        ])
        
        readonly_group, created = UserGroup.objects.get_or_create(
            name='Read_Only_Group',
            defaults={'description': 'Read-only access'}
        )
        readonly_group.permissions.set([
            permissions['view_resources'],
            permissions['export_data'],
        ])

    def create_business_units(self):
        """Create business units"""
        self.stdout.write('Creating business units...')
        
        bus_data = [
            ('HQ', 'Vernova CTO HQ'),
            ('GP', 'Gas Power'),
            ('REN', 'Renewables'),
            ('PC', 'Power Conversions'),
        ]
        
        for code, name in bus_data:
            BusinessUnit.objects.get_or_create(
                code=code,
                defaults={'name': name}
            )

    def create_aws_accounts(self):
        """Create AWS accounts"""
        self.stdout.write('Creating AWS accounts...')
        
        business_units = BusinessUnit.objects.all()
        regions = ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1']
        
        account_data = [
            ('************', 'HQ Production', 'HQ'),
            ('************', 'HQ Development', 'HQ'),
            ('************', 'Gas Power Production', 'GP'),
            ('************', 'Gas Power Development', 'GP'),
            ('************', 'Renewables Production', 'REN'),
            ('************', 'Renewables Development', 'REN'),
            ('************', 'Power Conversions Production', 'PC'),
            ('************', 'Power Conversions Development', 'PC'),
        ]
        
        for account_id, account_name, bu_code in account_data:
            bu = BusinessUnit.objects.get(code=bu_code)
            region = random.choice(regions)
            
            AWSAccount.objects.get_or_create(
                account_id=account_id,
                region=region,
                defaults={
                    'account_name': account_name,
                    'business_unit': bu,
                    'is_active': True,
                }
            )

    def create_sample_resources(self):
        """Create sample AWS resources"""
        self.stdout.write('Creating sample resources...')
        
        accounts = AWSAccount.objects.all()
        
        for account in accounts:
            # Create VPCs
            vpc = VPC.objects.create(
                vpc_id=f"vpc-{random.randint(100000, 999999)}",
                aws_account=account,
                cidr_block="10.0.0.0/16",
                state="available",
                is_default=False
            )
            
            # Create Security Groups
            sg = SecurityGroup.objects.create(
                group_id=f"sg-{random.randint(100000, 999999)}",
                group_name="default",
                description="Default security group",
                vpc=vpc
            )
            
            # Create EC2 instances
            for i in range(random.randint(3, 8)):
                instance = EC2Instance.objects.create(
                    resource_id=f"i-{random.randint(*********, *********)}",
                    resource_type='EC2',
                    resource_name=f"{account.business_unit.code}-instance-{i+1}",
                    aws_account=account,
                    region=account.region,
                    arn=f"arn:aws:ec2:{account.region}:{account.account_id}:instance/i-{random.randint(*********, *********)}",
                    state=random.choice(['running', 'stopped', 'pending']),
                    instance_type=random.choice(['t3.micro', 't3.small', 't3.medium', 'm5.large']),
                    platform=random.choice(['Linux/UNIX', 'Windows']),
                    vcpu_count=random.choice([1, 2, 4, 8]),
                    memory_size=random.choice([1024, 2048, 4096, 8192]),
                    vpc=vpc,
                    created_date=timezone.now() - timedelta(days=random.randint(1, 365))
                )
                instance.security_groups.add(sg)
                
                # Add tags
                Tag.objects.create(resource=instance, key='Environment', value=random.choice(['prod', 'dev', 'test']))
                Tag.objects.create(resource=instance, key='Owner', value=f"team-{account.business_unit.code.lower()}")
            
            # Create S3 buckets
            for i in range(random.randint(2, 5)):
                bucket = S3Bucket.objects.create(
                    resource_id=f"{account.business_unit.code.lower()}-bucket-{i+1}-{random.randint(1000, 9999)}",
                    resource_type='S3',
                    resource_name=f"{account.business_unit.code.lower()}-bucket-{i+1}-{random.randint(1000, 9999)}",
                    aws_account=account,
                    region=account.region,
                    arn=f"arn:aws:s3:::{account.business_unit.code.lower()}-bucket-{i+1}-{random.randint(1000, 9999)}",
                    state='available',
                    bucket_name=f"{account.business_unit.code.lower()}-bucket-{i+1}-{random.randint(1000, 9999)}",
                    versioning_status=random.choice(['Enabled', 'Suspended']),
                    encryption_status='AES256',
                    public_access_blocked=True,
                    created_date=timezone.now() - timedelta(days=random.randint(1, 365))
                )
                
                Tag.objects.create(resource=bucket, key='Purpose', value=random.choice(['backup', 'logs', 'data']))
            
            # Create Lambda functions
            for i in range(random.randint(1, 4)):
                lambda_func = LambdaFunction.objects.create(
                    resource_id=f"{account.business_unit.code.lower()}-function-{i+1}",
                    resource_type='LAMBDA',
                    resource_name=f"{account.business_unit.code.lower()}-function-{i+1}",
                    aws_account=account,
                    region=account.region,
                    arn=f"arn:aws:lambda:{account.region}:{account.account_id}:function:{account.business_unit.code.lower()}-function-{i+1}",
                    state='Active',
                    function_name=f"{account.business_unit.code.lower()}-function-{i+1}",
                    runtime=random.choice(['python3.9', 'nodejs18.x', 'java11']),
                    memory_size=random.choice([128, 256, 512, 1024]),
                    timeout=random.choice([30, 60, 300]),
                    created_date=timezone.now() - timedelta(days=random.randint(1, 365))
                )
                
                Tag.objects.create(resource=lambda_func, key='Runtime', value=lambda_func.runtime)

    def create_sample_users(self):
        """Create sample users"""
        self.stdout.write('Creating sample users...')
        
        user_groups = {
            'admin': UserGroup.objects.get(name='Admin_Group'),
            'ops': UserGroup.objects.get(name='Operation_Support_Group'),
            'readonly': UserGroup.objects.get(name='Read_Only_Group'),
        }
        
        users_data = [
            ('john.admin', 'John', 'Admin', '<EMAIL>', 'admin'),
            ('jane.ops', 'Jane', 'Operations', '<EMAIL>', 'ops'),
            ('bob.readonly', 'Bob', 'Reader', '<EMAIL>', 'readonly'),
        ]
        
        for username, first_name, last_name, email, group_type in users_data:
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'first_name': first_name,
                    'last_name': last_name,
                    'email': email,
                    'is_active': True,
                }
            )
            
            if created:
                user.set_password('password123')
                user.save()
            
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'user_group': user_groups[group_type],
                    'employee_id': f'EMP{random.randint(10000, 99999)}',
                    'department': f'{group_type.title()} Department',
                }
            )
