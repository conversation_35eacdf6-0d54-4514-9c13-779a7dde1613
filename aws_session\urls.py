from django.urls import path
from . import views

app_name = 'aws_session'

urlpatterns = [
    # Session management
    path('', views.SessionListView.as_view(), name='session_list'),
    path('create/', views.SessionCreateView.as_view(), name='session_create'),
    path('<int:pk>/', views.SessionDetailView.as_view(), name='session_detail'),
    path('<int:pk>/edit/', views.SessionEditView.as_view(), name='session_edit'),
    path('<int:pk>/delete/', views.SessionDeleteView.as_view(), name='session_delete'),
    path('<int:pk>/test/', views.SessionTestView.as_view(), name='session_test'),
    
    # Account management
    path('accounts/', views.AccountListView.as_view(), name='account_list'),
    path('accounts/import/', views.AccountImportView.as_view(), name='account_import'),
    path('accounts/<int:pk>/', views.AccountDetailView.as_view(), name='account_detail'),
    
    # API endpoints
    path('api/sessions/', views.SessionListAPIView.as_view(), name='api_session_list'),
    path('api/sessions/<int:pk>/test/', views.SessionTestAPIView.as_view(), name='api_session_test'),
    path('api/accounts/', views.AccountListAPIView.as_view(), name='api_account_list'),
]
