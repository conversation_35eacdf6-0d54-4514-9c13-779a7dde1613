# Generated by Django 4.2.7 on 2025-05-29 15:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SAMLConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entity_id', models.CharField(max_length=255, unique=True)),
                ('metadata_url', models.URLField(blank=True)),
                ('assertion_consumer_service_url', models.URLField()),
                ('single_logout_service_url', models.URLField(blank=True)),
                ('x509_cert', models.TextField(blank=True)),
                ('private_key', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'SAML Configuration',
                'verbose_name_plural': 'SAML Configurations',
            },
        ),
        migrations.CreateModel(
            name='UserGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('Admin_Group', 'Admin Group'), ('Operation_Support_Group', 'Operation Support Group'), ('Read_Only_Group', 'Read Only Group')], max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'User Group',
                'verbose_name_plural': 'User Groups',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(blank=True, max_length=50)),
                ('department', models.CharField(blank=True, max_length=100)),
                ('phone_number', models.CharField(blank=True, max_length=20)),
                ('is_saml_user', models.BooleanField(default=False)),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
                ('user_group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users', to='authentication.usergroup')),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
            },
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('view_resources', 'View Resources'), ('add_resources', 'Add Resources'), ('edit_resources', 'Edit Resources'), ('delete_resources', 'Delete Resources'), ('manage_aws_sessions', 'Manage AWS Sessions'), ('user_management', 'User Management'), ('system_configuration', 'System Configuration'), ('data_collection', 'Data Collection'), ('job_management', 'Job Management'), ('job_scheduling', 'Job Scheduling'), ('job_execution', 'Job Execution'), ('job_monitoring', 'Job Monitoring'), ('worker_management', 'Worker Management'), ('export_data', 'Export Data'), ('resource_tagging', 'Resource Tagging')], max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
                ('user_groups', models.ManyToManyField(related_name='permissions', to='authentication.usergroup')),
            ],
            options={
                'verbose_name': 'Permission',
                'verbose_name_plural': 'Permissions',
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('LOGIN', 'User Login'), ('LOGOUT', 'User Logout'), ('CREATE', 'Create Resource'), ('UPDATE', 'Update Resource'), ('DELETE', 'Delete Resource'), ('VIEW', 'View Resource'), ('EXPORT', 'Export Data'), ('JOB_CREATE', 'Create Job'), ('JOB_START', 'Start Job'), ('JOB_STOP', 'Stop Job'), ('CONFIG_CHANGE', 'Configuration Change')], max_length=20)),
                ('resource_type', models.CharField(blank=True, max_length=50)),
                ('resource_id', models.CharField(blank=True, max_length=255)),
                ('description', models.TextField(blank=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audit_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Audit Log',
                'verbose_name_plural': 'Audit Logs',
                'indexes': [models.Index(fields=['user', 'timestamp'], name='authenticat_user_id_3b88ca_idx'), models.Index(fields=['action', 'timestamp'], name='authenticat_action_4a725a_idx')],
            },
        ),
    ]
