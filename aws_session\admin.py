from django.contrib import admin
from .models import AWSSession, SessionTest


@admin.register(AWSSession)
class AWSSessionAdmin(admin.ModelAdmin):
    list_display = ['name', 'auth_method', 'region', 'is_active', 'created_by', 'created_at', 'last_used']
    list_filter = ['auth_method', 'region', 'is_active', 'created_at']
    search_fields = ['name', 'profile_name', 'role_arn']
    readonly_fields = ['created_at', 'updated_at', 'last_used']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'auth_method', 'region', 'is_active')
        }),
        ('Profile Authentication', {
            'fields': ('profile_name',),
            'classes': ('collapse',),
        }),
        ('Access Key Authentication', {
            'fields': ('access_key_id', 'secret_access_key'),
            'classes': ('collapse',),
        }),
        ('Assume Role Authentication', {
            'fields': ('role_arn', 'external_id', 'session_name'),
            'classes': ('collapse',),
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at', 'last_used'),
            'classes': ('collapse',),
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(SessionTest)
class SessionTestAdmin(admin.ModelAdmin):
    list_display = ['aws_session', 'test_type', 'is_successful', 'response_time', 'tested_at', 'tested_by']
    list_filter = ['test_type', 'is_successful', 'tested_at']
    search_fields = ['aws_session__name', 'error_message']
    readonly_fields = ['tested_at']

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.tested_by = request.user
        super().save_model(request, obj, form, change)
