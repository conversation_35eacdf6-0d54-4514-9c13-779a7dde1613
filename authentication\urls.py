from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

app_name = 'authentication'

urlpatterns = [
    # Authentication
    path('login/', views.CustomLoginView.as_view(), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('saml/login/', views.SAMLLoginView.as_view(), name='saml_login'),
    path('saml/acs/', views.SAMLAssertionConsumerView.as_view(), name='saml_acs'),
    path('saml/sls/', views.SAMLSingleLogoutView.as_view(), name='saml_sls'),
    
    # User management
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('users/', views.UserListView.as_view(), name='user_list'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user_detail'),
    path('users/<int:pk>/edit/', views.UserEditView.as_view(), name='user_edit'),
    
    # Groups and permissions
    path('groups/', views.UserGroupListView.as_view(), name='group_list'),
    path('groups/<int:pk>/', views.UserGroupDetailView.as_view(), name='group_detail'),
    path('permissions/', views.PermissionListView.as_view(), name='permission_list'),
    
    # Audit logs
    path('audit/', views.AuditLogListView.as_view(), name='audit_log_list'),
    
    # API endpoints
    path('api/user-info/', views.UserInfoAPIView.as_view(), name='api_user_info'),
    path('api/permissions/', views.UserPermissionsAPIView.as_view(), name='api_user_permissions'),
]
