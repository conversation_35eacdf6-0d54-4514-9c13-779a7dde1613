from django.contrib import admin
from .models import (
    BusinessUnit, AWSAccount, BaseResource, Tag, VPC, Subnet,
    SecurityGroup, SSMAgent, EC2Instance, S3Bucket, EKSCluster,
    ECSCluster, ECRRepository, LambdaFunction
)


@admin.register(BusinessUnit)
class BusinessUnitAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'description', 'created_at']
    list_filter = ['code', 'created_at']
    search_fields = ['code', 'name']


@admin.register(AWSAccount)
class AWSAccountAdmin(admin.ModelAdmin):
    list_display = ['account_id', 'account_name', 'business_unit', 'region', 'is_active', 'created_at']
    list_filter = ['business_unit', 'region', 'is_active', 'created_at']
    search_fields = ['account_id', 'account_name']


class TagInline(admin.TabularInline):
    model = Tag
    extra = 0


@admin.register(BaseResource)
class BaseResourceAdmin(admin.ModelAdmin):
    list_display = ['resource_name', 'resource_type', 'aws_account', 'region', 'state', 'last_updated']
    list_filter = ['resource_type', 'aws_account', 'region', 'state', 'last_updated']
    search_fields = ['resource_name', 'resource_id', 'arn']
    inlines = [TagInline]


@admin.register(EC2Instance)
class EC2InstanceAdmin(admin.ModelAdmin):
    list_display = ['resource_name', 'instance_type', 'platform', 'state', 'aws_account', 'last_updated']
    list_filter = ['instance_type', 'platform', 'state', 'aws_account', 'last_updated']
    search_fields = ['resource_name', 'resource_id', 'public_ip', 'private_ip']
    inlines = [TagInline]


@admin.register(S3Bucket)
class S3BucketAdmin(admin.ModelAdmin):
    list_display = ['bucket_name', 'versioning_status', 'encryption_status', 'public_access_blocked', 'aws_account']
    list_filter = ['versioning_status', 'encryption_status', 'public_access_blocked', 'aws_account']
    search_fields = ['bucket_name', 'resource_name']
    inlines = [TagInline]


@admin.register(EKSCluster)
class EKSClusterAdmin(admin.ModelAdmin):
    list_display = ['resource_name', 'version', 'status', 'aws_account', 'last_updated']
    list_filter = ['version', 'status', 'aws_account', 'last_updated']
    search_fields = ['resource_name', 'endpoint']
    inlines = [TagInline]


@admin.register(ECSCluster)
class ECSClusterAdmin(admin.ModelAdmin):
    list_display = ['resource_name', 'status', 'running_tasks_count', 'active_services_count', 'aws_account']
    list_filter = ['status', 'aws_account', 'last_updated']
    search_fields = ['resource_name']
    inlines = [TagInline]


@admin.register(ECRRepository)
class ECRRepositoryAdmin(admin.ModelAdmin):
    list_display = ['resource_name', 'repository_uri', 'registry_id', 'aws_account']
    list_filter = ['aws_account', 'last_updated']
    search_fields = ['resource_name', 'repository_uri']
    inlines = [TagInline]


@admin.register(LambdaFunction)
class LambdaFunctionAdmin(admin.ModelAdmin):
    list_display = ['function_name', 'runtime', 'memory_size', 'timeout', 'aws_account', 'last_updated']
    list_filter = ['runtime', 'aws_account', 'last_updated']
    search_fields = ['function_name', 'description']
    inlines = [TagInline]


@admin.register(VPC)
class VPCAdmin(admin.ModelAdmin):
    list_display = ['vpc_id', 'cidr_block', 'state', 'is_default', 'aws_account']
    list_filter = ['state', 'is_default', 'aws_account']
    search_fields = ['vpc_id', 'cidr_block']


@admin.register(SecurityGroup)
class SecurityGroupAdmin(admin.ModelAdmin):
    list_display = ['group_name', 'group_id', 'vpc', 'description']
    list_filter = ['vpc']
    search_fields = ['group_name', 'group_id', 'description']


@admin.register(SSMAgent)
class SSMAgentAdmin(admin.ModelAdmin):
    list_display = ['instance_id', 'status', 'current_version', 'is_latest', 'last_ping_date']
    list_filter = ['status', 'is_latest', 'last_ping_date']
    search_fields = ['instance_id']
