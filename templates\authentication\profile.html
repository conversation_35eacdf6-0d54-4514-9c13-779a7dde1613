{% extends 'base.html' %}
{% load static %}

{% block title %}User Profile - Cloud Central SS{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item active" aria-current="page">Profile</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header card-header-vernova">
                <h4 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>User Profile
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Account Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Username:</strong></td>
                                <td>{{ user.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>Full Name:</strong></td>
                                <td>{{ user.get_full_name|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ user.email|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Account Status:</strong></td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Last Login:</strong></td>
                                <td>
                                    {% if user.last_login %}
                                        {{ user.last_login|date:"M d, Y H:i" }}
                                    {% else %}
                                        <em>Never</em>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        {% if profile %}
                        <h6 class="text-muted">Profile Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>User Group:</strong></td>
                                <td>
                                    <span class="badge bg-vernova">{{ profile.user_group.name }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Employee ID:</strong></td>
                                <td>{{ profile.employee_id|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Department:</strong></td>
                                <td>{{ profile.department|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>{{ profile.phone_number|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Authentication:</strong></td>
                                <td>
                                    {% if profile.is_saml_user %}
                                        <span class="badge bg-info">SAML SSO</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Local</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                        {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Profile information not available. Please contact your administrator.
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                {% if profile %}
                <!-- Permissions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="text-muted">Permissions & Access</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-eye fa-2x text-vernova mb-2"></i>
                                        <h6>Resource Access</h6>
                                        <p class="small text-muted">
                                            {% if profile.is_admin %}
                                                Full access to all resources
                                            {% elif profile.is_operation_support %}
                                                Limited operational access
                                            {% else %}
                                                Read-only access
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-tasks fa-2x text-vernova mb-2"></i>
                                        <h6>Job Management</h6>
                                        <p class="small text-muted">
                                            {% if profile.is_admin %}
                                                Create, edit, and manage jobs
                                            {% elif profile.is_operation_support %}
                                                View and monitor jobs
                                            {% else %}
                                                View job status only
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-2x text-vernova mb-2"></i>
                                        <h6>Administration</h6>
                                        <p class="small text-muted">
                                            {% if profile.is_admin %}
                                                Full administrative access
                                            {% else %}
                                                No administrative access
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'inventory:dashboard' %}" class="btn btn-outline-vernova">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            
                            <div>
                                {% if not profile.is_saml_user %}
                                <button class="btn btn-outline-vernova" onclick="changePassword()">
                                    <i class="fas fa-key me-2"></i>Change Password
                                </button>
                                {% endif %}
                                
                                <a href="{% url 'authentication:logout' %}" class="btn btn-outline-danger">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function changePassword() {
    showNotification('Password change functionality not implemented yet.', 'info');
}
</script>
{% endblock %}
