{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard - Cloud Central SS{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item active" aria-current="page">
            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
        </li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="dashboard-auto-refresh">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-vernova text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="card-title mb-1">
                                <i class="fas fa-cloud me-2"></i>Welcome to Cloud Central SS
                            </h2>
                            <p class="card-text mb-0">
                                AWS Resource Inventory Management System for GE Vernova
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <img src="{% static 'images/GEV-e06a174f.png' %}" alt="GE Vernova" height="60" class="opacity-75">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Resource Overview Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">Total Resources</h6>
                        <i class="fas fa-server text-vernova fa-2x"></i>
                    </div>
                    <div class="dashboard-stat" data-stat="total">{{ resource_counts.total }}</div>
                    <small class="text-muted">Across all accounts</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">EC2 Instances</h6>
                        <i class="fas fa-desktop resource-ec2 fa-2x"></i>
                    </div>
                    <div class="dashboard-stat" data-stat="ec2">{{ resource_counts.ec2 }}</div>
                    <small class="text-muted">Virtual machines</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">S3 Buckets</h6>
                        <i class="fas fa-hdd resource-s3 fa-2x"></i>
                    </div>
                    <div class="dashboard-stat" data-stat="s3">{{ resource_counts.s3 }}</div>
                    <small class="text-muted">Storage buckets</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">Lambda Functions</h6>
                        <i class="fas fa-bolt resource-lambda fa-2x"></i>
                    </div>
                    <div class="dashboard-stat" data-stat="lambda">{{ resource_counts.lambda }}</div>
                    <small class="text-muted">Serverless functions</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Container Services Row -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">EKS Clusters</h6>
                        <i class="fas fa-dharmachakra resource-eks fa-2x"></i>
                    </div>
                    <div class="dashboard-stat" data-stat="eks">{{ resource_counts.eks }}</div>
                    <small class="text-muted">Kubernetes clusters</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">ECS Clusters</h6>
                        <i class="fas fa-cubes resource-ecs fa-2x"></i>
                    </div>
                    <div class="dashboard-stat" data-stat="ecs">{{ resource_counts.ecs }}</div>
                    <small class="text-muted">Container clusters</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">ECR Repositories</h6>
                        <i class="fas fa-archive resource-ecr fa-2x"></i>
                    </div>
                    <div class="dashboard-stat" data-stat="ecr">{{ resource_counts.ecr }}</div>
                    <small class="text-muted">Container registries</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Details Row -->
    <div class="row">
        <!-- Business Units -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header card-header-vernova">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>Business Units
                    </h5>
                </div>
                <div class="card-body">
                    {% for bu in bu_counts %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <strong>{{ bu.name }}</strong>
                            <small class="text-muted d-block">{{ bu.code }}</small>
                        </div>
                        <span class="badge bg-vernova">{{ bu.account_count }} account{{ bu.account_count|pluralize }}</span>
                    </div>
                    {% empty %}
                    <p class="text-muted">No business units configured.</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Recent Resources -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header card-header-vernova">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Resources
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody>
                                {% for resource in recent_resources %}
                                <tr>
                                    <td>
                                        <i class="fas fa-server resource-{{ resource.resource_type|lower }} me-2"></i>
                                        <a href="{% url 'inventory:resource_detail' resource.pk %}" class="text-decoration-none">
                                            {{ resource.resource_name|truncatechars:30 }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ resource.resource_type }}</span>
                                    </td>
                                    <td class="text-end">
                                        <small class="text-muted">{{ resource.last_updated|timesince }} ago</small>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center text-muted">No resources found.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Region Distribution -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header card-header-vernova">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-globe me-2"></i>Resource Distribution by Region
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for region in region_distribution %}
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                <div>
                                    <strong>{{ region.region }}</strong>
                                </div>
                                <span class="badge bg-vernova">{{ region.count }}</span>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <p class="text-muted text-center">No resources found.</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header card-header-vernova">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'inventory:resource_list' %}" class="btn btn-outline-vernova w-100">
                                <i class="fas fa-list me-2"></i>View All Resources
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'inventory:resource_search' %}" class="btn btn-outline-vernova w-100">
                                <i class="fas fa-search me-2"></i>Advanced Search
                            </a>
                        </div>
                        {% if user.profile.is_admin or user.profile.is_operation_support %}
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'jobs:dashboard' %}" class="btn btn-outline-vernova w-100">
                                <i class="fas fa-tasks me-2"></i>Jobs Dashboard
                            </a>
                        </div>
                        {% endif %}
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'inventory:resource_export' %}" class="btn btn-outline-vernova w-100">
                                <i class="fas fa-download me-2"></i>Export Data
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-refresh dashboard every 30 seconds
    setInterval(function() {
        location.reload();
    }, 30000);
});
</script>
{% endblock %}
