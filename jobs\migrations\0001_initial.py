# Generated by Django 4.2.7 on 2025-05-29 15:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('aws_session', '0001_initial'),
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Job',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('job_type', models.CharField(choices=[('full_collection', 'Full Resource Collection'), ('incremental_collection', 'Incremental Collection'), ('specific_resource', 'Specific Resource Type'), ('account_specific', 'Account Specific Collection')], max_length=30)),
                ('resource_types', models.J<PERSON><PERSON><PERSON>(default=list)),
                ('parallel_accounts', models.IntegerField(default=3)),
                ('parallel_regions', models.IntegerField(default=2)),
                ('parallel_resources', models.IntegerField(default=5)),
                ('timeout_minutes', models.IntegerField(default=60)),
                ('retry_count', models.IntegerField(default=3)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('aws_accounts', models.ManyToManyField(blank=True, to='inventory.awsaccount')),
                ('aws_session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='aws_session.awssession')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_jobs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Job',
                'verbose_name_plural': 'Jobs',
            },
        ),
        migrations.CreateModel(
            name='JobExecution',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('timeout', 'Timeout')], default='pending', max_length=20)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('total_tasks', models.IntegerField(default=0)),
                ('completed_tasks', models.IntegerField(default=0)),
                ('failed_tasks', models.IntegerField(default=0)),
                ('progress_percentage', models.FloatField(default=0.0)),
                ('resources_collected', models.IntegerField(default=0)),
                ('resources_updated', models.IntegerField(default=0)),
                ('resources_failed', models.IntegerField(default=0)),
                ('error_message', models.TextField(blank=True)),
                ('error_details', models.JSONField(blank=True, default=dict)),
                ('celery_task_id', models.CharField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='jobs.job')),
                ('started_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='started_executions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Job Execution',
                'verbose_name_plural': 'Job Executions',
            },
        ),
        migrations.CreateModel(
            name='JobSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('schedule_type', models.CharField(choices=[('once', 'Run Once'), ('hourly', 'Hourly'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('cron', 'Custom Cron Expression')], max_length=20)),
                ('cron_expression', models.CharField(blank=True, max_length=100)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('is_enabled', models.BooleanField(default=True)),
                ('next_run', models.DateTimeField(blank=True, null=True)),
                ('last_run', models.DateTimeField(blank=True, null=True)),
                ('job', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='schedule', to='jobs.job')),
            ],
            options={
                'verbose_name': 'Job Schedule',
                'verbose_name_plural': 'Job Schedules',
            },
        ),
        migrations.CreateModel(
            name='JobQueue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('priority', models.IntegerField(choices=[(1, 'Low'), (2, 'Normal'), (3, 'High'), (4, 'Critical')], default=2)),
                ('queued_at', models.DateTimeField(auto_now_add=True)),
                ('estimated_duration', models.IntegerField(blank=True, null=True)),
                ('dependencies', models.ManyToManyField(blank=True, to='jobs.jobqueue')),
                ('job_execution', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='queue_entry', to='jobs.jobexecution')),
            ],
            options={
                'verbose_name': 'Job Queue',
                'verbose_name_plural': 'Job Queue',
                'ordering': ['-priority', 'queued_at'],
            },
        ),
        migrations.CreateModel(
            name='CollectionMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_execution_time', models.FloatField(default=0.0)),
                ('average_task_time', models.FloatField(default=0.0)),
                ('peak_memory_usage', models.FloatField(default=0.0)),
                ('cpu_usage_percentage', models.FloatField(default=0.0)),
                ('total_api_calls', models.IntegerField(default=0)),
                ('successful_api_calls', models.IntegerField(default=0)),
                ('failed_api_calls', models.IntegerField(default=0)),
                ('api_rate_limit_hits', models.IntegerField(default=0)),
                ('total_resources_found', models.IntegerField(default=0)),
                ('new_resources_added', models.IntegerField(default=0)),
                ('existing_resources_updated', models.IntegerField(default=0)),
                ('resources_deleted', models.IntegerField(default=0)),
                ('workers_used', models.IntegerField(default=0)),
                ('parallel_tasks_peak', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('job_execution', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='jobs.jobexecution')),
            ],
            options={
                'verbose_name': 'Collection Metrics',
                'verbose_name_plural': 'Collection Metrics',
            },
        ),
        migrations.CreateModel(
            name='TaskResult',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('task_type', models.CharField(choices=[('account_collection', 'Account Collection'), ('resource_collection', 'Resource Collection'), ('data_processing', 'Data Processing'), ('validation', 'Data Validation')], max_length=30)),
                ('task_name', models.CharField(max_length=200)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed'), ('skipped', 'Skipped')], default='pending', max_length=20)),
                ('aws_account_id', models.CharField(blank=True, max_length=12)),
                ('region', models.CharField(blank=True, max_length=20)),
                ('resource_type', models.CharField(blank=True, max_length=20)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('execution_time', models.FloatField(blank=True, null=True)),
                ('items_processed', models.IntegerField(default=0)),
                ('items_successful', models.IntegerField(default=0)),
                ('items_failed', models.IntegerField(default=0)),
                ('error_message', models.TextField(blank=True)),
                ('error_details', models.JSONField(blank=True, default=dict)),
                ('celery_task_id', models.CharField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('job_execution', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='task_results', to='jobs.jobexecution')),
            ],
            options={
                'verbose_name': 'Task Result',
                'verbose_name_plural': 'Task Results',
                'indexes': [models.Index(fields=['job_execution', 'status'], name='jobs_taskre_job_exe_d9d62c_idx'), models.Index(fields=['task_type', 'status'], name='jobs_taskre_task_ty_912e95_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='jobexecution',
            index=models.Index(fields=['job', 'status'], name='jobs_jobexe_job_id_67aea3_idx'),
        ),
        migrations.AddIndex(
            model_name='jobexecution',
            index=models.Index(fields=['started_at', 'status'], name='jobs_jobexe_started_f8e989_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['job_type', 'is_active'], name='jobs_job_job_typ_c14aee_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['created_by', 'created_at'], name='jobs_job_created_197740_idx'),
        ),
    ]
