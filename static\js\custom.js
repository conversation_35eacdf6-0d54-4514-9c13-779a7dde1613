// Cloud Central SS - Custom JavaScript

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    $('.alert').each(function() {
        if (!$(this).hasClass('alert-permanent')) {
            setTimeout(() => {
                $(this).fadeOut();
            }, 5000);
        }
    });

    // Search functionality
    $('#resource-search').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterResources(searchTerm);
    });

    // Filter functionality
    $('.filter-checkbox').on('change', function() {
        applyFilters();
    });

    // Real-time job status updates
    if (window.location.pathname.includes('/jobs/')) {
        startJobStatusUpdates();
    }

    // Confirm delete actions
    $('.btn-delete').on('click', function(e) {
        if (!confirm('Are you sure you want to delete this item?')) {
            e.preventDefault();
        }
    });

    // Auto-refresh for dashboard
    if ($('.dashboard-auto-refresh').length > 0) {
        setInterval(refreshDashboard, 30000); // Refresh every 30 seconds
    }
});

// Resource filtering function
function filterResources(searchTerm) {
    $('.resource-row').each(function() {
        const resourceName = $(this).find('.resource-name').text().toLowerCase();
        const resourceType = $(this).find('.resource-type').text().toLowerCase();
        const accountName = $(this).find('.account-name').text().toLowerCase();
        
        if (resourceName.includes(searchTerm) || 
            resourceType.includes(searchTerm) || 
            accountName.includes(searchTerm)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
    
    updateResourceCount();
}

// Apply filters based on checkboxes
function applyFilters() {
    const selectedTypes = [];
    const selectedAccounts = [];
    const selectedRegions = [];
    const selectedStates = [];
    
    $('.filter-resource-type:checked').each(function() {
        selectedTypes.push($(this).val());
    });
    
    $('.filter-account:checked').each(function() {
        selectedAccounts.push($(this).val());
    });
    
    $('.filter-region:checked').each(function() {
        selectedRegions.push($(this).val());
    });
    
    $('.filter-state:checked').each(function() {
        selectedStates.push($(this).val());
    });
    
    $('.resource-row').each(function() {
        let show = true;
        
        if (selectedTypes.length > 0) {
            const resourceType = $(this).data('resource-type');
            if (!selectedTypes.includes(resourceType)) {
                show = false;
            }
        }
        
        if (selectedAccounts.length > 0 && show) {
            const account = $(this).data('account');
            if (!selectedAccounts.includes(account)) {
                show = false;
            }
        }
        
        if (selectedRegions.length > 0 && show) {
            const region = $(this).data('region');
            if (!selectedRegions.includes(region)) {
                show = false;
            }
        }
        
        if (selectedStates.length > 0 && show) {
            const state = $(this).data('state');
            if (!selectedStates.includes(state)) {
                show = false;
            }
        }
        
        if (show) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
    
    updateResourceCount();
}

// Update resource count display
function updateResourceCount() {
    const visibleCount = $('.resource-row:visible').length;
    const totalCount = $('.resource-row').length;
    $('#resource-count').text(`Showing ${visibleCount} of ${totalCount} resources`);
}

// Job status updates via WebSocket or polling
function startJobStatusUpdates() {
    // Check if WebSocket is available
    if (typeof WebSocket !== 'undefined') {
        setupWebSocketUpdates();
    } else {
        // Fallback to polling
        setInterval(pollJobStatus, 5000);
    }
}

function setupWebSocketUpdates() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/jobs/`;
    
    const socket = new WebSocket(wsUrl);
    
    socket.onmessage = function(event) {
        const data = JSON.parse(event.data);
        updateJobStatus(data);
    };
    
    socket.onclose = function() {
        // Reconnect after 5 seconds
        setTimeout(setupWebSocketUpdates, 5000);
    };
}

function pollJobStatus() {
    $.ajax({
        url: '/jobs/api/executions/',
        method: 'GET',
        success: function(data) {
            data.results.forEach(function(execution) {
                updateJobStatus(execution);
            });
        }
    });
}

function updateJobStatus(execution) {
    const row = $(`.job-execution[data-execution-id="${execution.id}"]`);
    if (row.length > 0) {
        row.find('.job-status').html(getStatusBadge(execution.status));
        row.find('.job-progress').html(getProgressBar(execution.progress_percentage));
        
        if (execution.status === 'completed' || execution.status === 'failed') {
            row.find('.job-actions .btn-stop').hide();
            row.find('.job-actions .btn-retry').show();
        }
    }
}

function getStatusBadge(status) {
    const badges = {
        'pending': '<span class="badge bg-warning">Pending</span>',
        'running': '<span class="badge bg-info">Running</span>',
        'completed': '<span class="badge bg-success">Completed</span>',
        'failed': '<span class="badge bg-danger">Failed</span>',
        'cancelled': '<span class="badge bg-secondary">Cancelled</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function getProgressBar(percentage) {
    return `
        <div class="progress" style="height: 20px;">
            <div class="progress-bar progress-vernova" role="progressbar" 
                 style="width: ${percentage}%" aria-valuenow="${percentage}" 
                 aria-valuemin="0" aria-valuemax="100">
                ${percentage.toFixed(1)}%
            </div>
        </div>
    `;
}

// Dashboard refresh function
function refreshDashboard() {
    $.ajax({
        url: window.location.pathname + '?ajax=1',
        method: 'GET',
        success: function(data) {
            if (data.stats) {
                updateDashboardStats(data.stats);
            }
        }
    });
}

function updateDashboardStats(stats) {
    Object.keys(stats).forEach(function(key) {
        $(`.dashboard-stat[data-stat="${key}"]`).text(stats[key]);
    });
}

// Export functionality
function exportResources(format) {
    const filters = getActiveFilters();
    const url = `/inventory/export/?format=${format}&${$.param(filters)}`;
    window.open(url, '_blank');
}

function getActiveFilters() {
    const filters = {};
    
    $('.filter-checkbox:checked').each(function() {
        const filterType = $(this).data('filter-type');
        const filterValue = $(this).val();
        
        if (!filters[filterType]) {
            filters[filterType] = [];
        }
        filters[filterType].push(filterValue);
    });
    
    const searchTerm = $('#resource-search').val();
    if (searchTerm) {
        filters.search = searchTerm;
    }
    
    return filters;
}

// Utility functions
function showLoading(element) {
    $(element).html('<div class="spinner-vernova mx-auto"></div>');
}

function hideLoading() {
    $('.spinner-vernova').remove();
}

function showNotification(message, type = 'info') {
    const alertClass = `alert-${type}`;
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('.container-fluid').prepend(alert);
    
    setTimeout(() => {
        alert.fadeOut();
    }, 5000);
}

// CSRF token setup for AJAX requests
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

const csrftoken = getCookie('csrftoken');

$.ajaxSetup({
    beforeSend: function(xhr, settings) {
        if (!(/^http:.*/.test(settings.url) || /^https:.*/.test(settings.url))) {
            xhr.setRequestHeader("X-CSRFToken", csrftoken);
        }
    }
});
