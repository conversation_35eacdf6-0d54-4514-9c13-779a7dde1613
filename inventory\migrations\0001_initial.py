# Generated by Django 4.2.7 on 2025-05-29 15:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AWSAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_id', models.CharField(max_length=12, unique=True)),
                ('account_name', models.CharField(max_length=100)),
                ('region', models.CharField(default='us-east-1', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'AWS Account',
                'verbose_name_plural': 'AWS Accounts',
            },
        ),
        migrations.CreateModel(
            name='BaseResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('resource_id', models.CharField(max_length=255)),
                ('resource_type', models.CharField(choices=[('EC2', 'EC2 Instance'), ('S3', 'S3 Bucket'), ('EKS', 'EKS Cluster'), ('ECS', 'ECS Cluster'), ('ECR', 'ECR Repository'), ('LAMBDA', 'Lambda Function')], max_length=20)),
                ('resource_name', models.CharField(max_length=255)),
                ('region', models.CharField(max_length=20)),
                ('arn', models.CharField(blank=True, max_length=500)),
                ('state', models.CharField(blank=True, max_length=50)),
                ('created_date', models.DateTimeField(blank=True, null=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('data_collected_at', models.DateTimeField(auto_now_add=True)),
                ('aws_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='inventory.awsaccount')),
            ],
            options={
                'verbose_name': 'Base Resource',
                'verbose_name_plural': 'Base Resources',
            },
        ),
        migrations.CreateModel(
            name='BusinessUnit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(choices=[('HQ', 'Vernova CTO HQ'), ('GP', 'Gas Power'), ('REN', 'Renewables'), ('PC', 'Power Conversions')], max_length=10, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Business Unit',
                'verbose_name_plural': 'Business Units',
            },
        ),
        migrations.CreateModel(
            name='SSMAgent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('instance_id', models.CharField(max_length=255, unique=True)),
                ('status', models.CharField(choices=[('Online', 'Online'), ('Offline', 'Offline'), ('Unknown', 'Unknown')], max_length=20)),
                ('current_version', models.CharField(blank=True, max_length=50)),
                ('latest_version', models.CharField(blank=True, max_length=50)),
                ('is_latest', models.BooleanField(default=False)),
                ('last_ping_date', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='ECRRepository',
            fields=[
                ('baseresource_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='inventory.baseresource')),
                ('repository_uri', models.URLField(blank=True)),
                ('registry_id', models.CharField(blank=True, max_length=50)),
                ('image_scanning_configuration', models.JSONField(blank=True, default=dict)),
                ('image_tag_mutability', models.CharField(blank=True, max_length=20)),
            ],
            options={
                'verbose_name': 'ECR Repository',
                'verbose_name_plural': 'ECR Repositories',
            },
            bases=('inventory.baseresource',),
        ),
        migrations.CreateModel(
            name='ECSCluster',
            fields=[
                ('baseresource_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='inventory.baseresource')),
                ('status', models.CharField(blank=True, max_length=20)),
                ('running_tasks_count', models.IntegerField(default=0)),
                ('pending_tasks_count', models.IntegerField(default=0)),
                ('active_services_count', models.IntegerField(default=0)),
                ('registered_container_instances_count', models.IntegerField(default=0)),
            ],
            options={
                'verbose_name': 'ECS Cluster',
                'verbose_name_plural': 'ECS Clusters',
            },
            bases=('inventory.baseresource',),
        ),
        migrations.CreateModel(
            name='EKSCluster',
            fields=[
                ('baseresource_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='inventory.baseresource')),
                ('version', models.CharField(blank=True, max_length=20)),
                ('endpoint', models.URLField(blank=True)),
                ('role_arn', models.CharField(blank=True, max_length=500)),
                ('vpc_config', models.JSONField(blank=True, default=dict)),
                ('status', models.CharField(blank=True, max_length=20)),
            ],
            options={
                'verbose_name': 'EKS Cluster',
                'verbose_name_plural': 'EKS Clusters',
            },
            bases=('inventory.baseresource',),
        ),
        migrations.CreateModel(
            name='LambdaFunction',
            fields=[
                ('baseresource_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='inventory.baseresource')),
                ('function_name', models.CharField(max_length=255)),
                ('runtime', models.CharField(blank=True, max_length=50)),
                ('handler', models.CharField(blank=True, max_length=255)),
                ('code_size', models.BigIntegerField(blank=True, null=True)),
                ('description', models.TextField(blank=True)),
                ('timeout', models.IntegerField(blank=True, null=True)),
                ('memory_size', models.IntegerField(blank=True, null=True)),
                ('last_modified', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Lambda Function',
                'verbose_name_plural': 'Lambda Functions',
            },
            bases=('inventory.baseresource',),
        ),
        migrations.CreateModel(
            name='S3Bucket',
            fields=[
                ('baseresource_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='inventory.baseresource')),
                ('bucket_name', models.CharField(max_length=255, unique=True)),
                ('creation_date', models.DateTimeField(blank=True, null=True)),
                ('versioning_status', models.CharField(blank=True, max_length=20)),
                ('encryption_status', models.CharField(blank=True, max_length=50)),
                ('public_access_blocked', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'S3 Bucket',
                'verbose_name_plural': 'S3 Buckets',
            },
            bases=('inventory.baseresource',),
        ),
        migrations.CreateModel(
            name='VPC',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vpc_id', models.CharField(max_length=255)),
                ('cidr_block', models.CharField(max_length=50)),
                ('state', models.CharField(max_length=20)),
                ('is_default', models.BooleanField(default=False)),
                ('aws_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vpcs', to='inventory.awsaccount')),
            ],
            options={
                'unique_together': {('vpc_id', 'aws_account')},
            },
        ),
        migrations.AddField(
            model_name='awsaccount',
            name='business_unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='aws_accounts', to='inventory.businessunit'),
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=255)),
                ('value', models.CharField(blank=True, max_length=255)),
                ('resource', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tags', to='inventory.baseresource')),
            ],
            options={
                'verbose_name': 'Tag',
                'verbose_name_plural': 'Tags',
                'indexes': [models.Index(fields=['key', 'value'], name='inventory_t_key_9e270f_idx')],
                'unique_together': {('resource', 'key')},
            },
        ),
        migrations.CreateModel(
            name='Subnet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subnet_id', models.CharField(max_length=255)),
                ('cidr_block', models.CharField(max_length=50)),
                ('availability_zone', models.CharField(max_length=50)),
                ('state', models.CharField(max_length=20)),
                ('vpc', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subnets', to='inventory.vpc')),
            ],
            options={
                'unique_together': {('subnet_id', 'vpc')},
            },
        ),
        migrations.CreateModel(
            name='SecurityGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('group_id', models.CharField(max_length=255)),
                ('group_name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('vpc', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='security_groups', to='inventory.vpc')),
            ],
            options={
                'unique_together': {('group_id', 'vpc')},
            },
        ),
        migrations.CreateModel(
            name='EC2Instance',
            fields=[
                ('baseresource_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='inventory.baseresource')),
                ('instance_type', models.CharField(max_length=50)),
                ('platform', models.CharField(blank=True, max_length=20)),
                ('platform_details', models.CharField(blank=True, max_length=100)),
                ('os_version', models.CharField(blank=True, max_length=100)),
                ('kernel_version', models.CharField(blank=True, max_length=100)),
                ('vcpu_count', models.IntegerField(blank=True, null=True)),
                ('memory_size', models.IntegerField(blank=True, null=True)),
                ('public_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('private_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('security_groups', models.ManyToManyField(blank=True, to='inventory.securitygroup')),
                ('ssm_agent', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.ssmagent')),
                ('subnet', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.subnet')),
                ('vpc', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.vpc')),
            ],
            options={
                'verbose_name': 'EC2 Instance',
                'verbose_name_plural': 'EC2 Instances',
            },
            bases=('inventory.baseresource',),
        ),
        migrations.AddIndex(
            model_name='baseresource',
            index=models.Index(fields=['resource_type', 'aws_account'], name='inventory_b_resourc_d18a8f_idx'),
        ),
        migrations.AddIndex(
            model_name='baseresource',
            index=models.Index(fields=['resource_name'], name='inventory_b_resourc_59c60c_idx'),
        ),
        migrations.AddIndex(
            model_name='baseresource',
            index=models.Index(fields=['state'], name='inventory_b_state_23893c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='baseresource',
            unique_together={('resource_id', 'aws_account', 'resource_type')},
        ),
        migrations.AlterUniqueTogether(
            name='awsaccount',
            unique_together={('account_id', 'region')},
        ),
    ]
