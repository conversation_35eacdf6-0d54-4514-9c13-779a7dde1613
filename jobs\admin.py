from django.contrib import admin
from .models import Job, JobExecution, JobSchedule, TaskResult, CollectionMetrics, JobQueue


class JobScheduleInline(admin.StackedInline):
    model = JobSchedule
    can_delete = False
    verbose_name_plural = 'Job Schedule'


@admin.register(Job)
class JobAdmin(admin.ModelAdmin):
    list_display = ['name', 'job_type', 'aws_session', 'is_active', 'created_by', 'created_at']
    list_filter = ['job_type', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    filter_horizontal = ['aws_accounts']
    inlines = [JobScheduleInline]
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'job_type', 'is_active')
        }),
        ('Configuration', {
            'fields': ('resource_types', 'aws_accounts', 'aws_session')
        }),
        ('Performance Settings', {
            'fields': ('parallel_accounts', 'parallel_regions', 'parallel_resources', 'timeout_minutes', 'retry_count'),
            'classes': ('collapse',),
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class TaskResultInline(admin.TabularInline):
    model = TaskResult
    extra = 0
    readonly_fields = ['task_name', 'status', 'started_at', 'completed_at', 'execution_time']


class CollectionMetricsInline(admin.StackedInline):
    model = CollectionMetrics
    can_delete = False
    readonly_fields = ['total_execution_time', 'average_task_time', 'peak_memory_usage']


@admin.register(JobExecution)
class JobExecutionAdmin(admin.ModelAdmin):
    list_display = ['job', 'status', 'progress_percentage', 'started_at', 'completed_at', 'started_by']
    list_filter = ['status', 'started_at', 'job__job_type']
    search_fields = ['job__name', 'error_message']
    readonly_fields = ['started_at', 'completed_at', 'created_at', 'duration', 'celery_task_id']
    inlines = [TaskResultInline, CollectionMetricsInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('job', 'status', 'started_by')
        }),
        ('Progress', {
            'fields': ('total_tasks', 'completed_tasks', 'failed_tasks', 'progress_percentage')
        }),
        ('Results', {
            'fields': ('resources_collected', 'resources_updated', 'resources_failed')
        }),
        ('Timing', {
            'fields': ('started_at', 'completed_at', 'duration'),
            'classes': ('collapse',),
        }),
        ('Error Information', {
            'fields': ('error_message', 'error_details'),
            'classes': ('collapse',),
        }),
        ('Technical Details', {
            'fields': ('celery_task_id', 'created_at'),
            'classes': ('collapse',),
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.started_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(JobSchedule)
class JobScheduleAdmin(admin.ModelAdmin):
    list_display = ['job', 'schedule_type', 'is_enabled', 'next_run', 'last_run']
    list_filter = ['schedule_type', 'is_enabled', 'next_run']
    search_fields = ['job__name', 'cron_expression']


@admin.register(TaskResult)
class TaskResultAdmin(admin.ModelAdmin):
    list_display = ['task_name', 'task_type', 'status', 'aws_account_id', 'region', 'execution_time']
    list_filter = ['task_type', 'status', 'region', 'started_at']
    search_fields = ['task_name', 'aws_account_id', 'error_message']
    readonly_fields = ['started_at', 'completed_at', 'execution_time', 'celery_task_id']


@admin.register(CollectionMetrics)
class CollectionMetricsAdmin(admin.ModelAdmin):
    list_display = ['job_execution', 'total_execution_time', 'total_api_calls', 'total_resources_found', 'workers_used']
    readonly_fields = ['created_at']


@admin.register(JobQueue)
class JobQueueAdmin(admin.ModelAdmin):
    list_display = ['job_execution', 'priority', 'queued_at', 'estimated_duration']
    list_filter = ['priority', 'queued_at']
    filter_horizontal = ['dependencies']
