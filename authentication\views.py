from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout
from django.contrib.auth.views import LoginView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView, ListView, DetailView, UpdateView
from django.contrib.auth.models import User
from django.http import JsonResponse
from django.contrib import messages
from django.urls import reverse_lazy

from .models import UserProfile, UserGroup, Permission, AuditLog


class CustomLoginView(LoginView):
    template_name = 'authentication/login.html'
    redirect_authenticated_user = True

    def form_valid(self, form):
        # Log the login
        user = form.get_user()
        AuditLog.objects.create(
            user=user,
            action='LOGIN',
            description=f'User {user.username} logged in',
            ip_address=self.get_client_ip(),
            user_agent=self.request.META.get('HTTP_USER_AGENT', '')
        )
        return super().form_valid(form)

    def get_client_ip(self):
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip


class SAMLLoginView(TemplateView):
    template_name = 'authentication/saml_login.html'

    def get(self, request, *args, **kwargs):
        # SAML login implementation would go here
        # For now, redirect to regular login
        messages.info(request, 'SAML SSO is not configured yet. Please use regular login.')
        return redirect('authentication:login')


class SAMLAssertionConsumerView(TemplateView):
    def post(self, request, *args, **kwargs):
        # SAML ACS implementation would go here
        return JsonResponse({'status': 'not_implemented'})


class SAMLSingleLogoutView(TemplateView):
    def get(self, request, *args, **kwargs):
        # SAML SLS implementation would go here
        logout(request)
        return redirect('authentication:login')


class UserProfileView(LoginRequiredMixin, TemplateView):
    template_name = 'authentication/profile.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            context['profile'] = self.request.user.profile
        except UserProfile.DoesNotExist:
            context['profile'] = None
        return context


class UserListView(LoginRequiredMixin, ListView):
    model = User
    template_name = 'authentication/user_list.html'
    context_object_name = 'users'
    paginate_by = 25

    def dispatch(self, request, *args, **kwargs):
        # Check if user has permission to view users
        if not hasattr(request.user, 'profile') or not request.user.profile.is_admin:
            messages.error(request, 'You do not have permission to view users.')
            return redirect('inventory:dashboard')
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return User.objects.select_related('profile', 'profile__user_group').order_by('username')


class UserDetailView(LoginRequiredMixin, DetailView):
    model = User
    template_name = 'authentication/user_detail.html'
    context_object_name = 'user_obj'

    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'profile') or not request.user.profile.is_admin:
            messages.error(request, 'You do not have permission to view user details.')
            return redirect('inventory:dashboard')
        return super().dispatch(request, *args, **kwargs)


class UserEditView(LoginRequiredMixin, UpdateView):
    model = User
    template_name = 'authentication/user_edit.html'
    fields = ['first_name', 'last_name', 'email', 'is_active']
    success_url = reverse_lazy('authentication:user_list')

    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'profile') or not request.user.profile.is_admin:
            messages.error(request, 'You do not have permission to edit users.')
            return redirect('inventory:dashboard')
        return super().dispatch(request, *args, **kwargs)


class UserGroupListView(LoginRequiredMixin, ListView):
    model = UserGroup
    template_name = 'authentication/group_list.html'
    context_object_name = 'groups'

    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'profile') or not request.user.profile.is_admin:
            messages.error(request, 'You do not have permission to view groups.')
            return redirect('inventory:dashboard')
        return super().dispatch(request, *args, **kwargs)


class UserGroupDetailView(LoginRequiredMixin, DetailView):
    model = UserGroup
    template_name = 'authentication/group_detail.html'
    context_object_name = 'group'

    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'profile') or not request.user.profile.is_admin:
            messages.error(request, 'You do not have permission to view group details.')
            return redirect('inventory:dashboard')
        return super().dispatch(request, *args, **kwargs)


class PermissionListView(LoginRequiredMixin, ListView):
    model = Permission
    template_name = 'authentication/permission_list.html'
    context_object_name = 'permissions'

    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'profile') or not request.user.profile.is_admin:
            messages.error(request, 'You do not have permission to view permissions.')
            return redirect('inventory:dashboard')
        return super().dispatch(request, *args, **kwargs)


class AuditLogListView(LoginRequiredMixin, ListView):
    model = AuditLog
    template_name = 'authentication/audit_log_list.html'
    context_object_name = 'logs'
    paginate_by = 50

    def dispatch(self, request, *args, **kwargs):
        if not hasattr(request.user, 'profile') or not request.user.profile.is_admin:
            messages.error(request, 'You do not have permission to view audit logs.')
            return redirect('inventory:dashboard')
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return AuditLog.objects.select_related('user').order_by('-timestamp')


# API Views
class UserInfoAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        user = request.user
        data = {
            'id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'email': user.email,
            'is_active': user.is_active,
        }

        if hasattr(user, 'profile'):
            data['profile'] = {
                'user_group': user.profile.user_group.name,
                'employee_id': user.profile.employee_id,
                'department': user.profile.department,
                'is_admin': user.profile.is_admin,
                'is_operation_support': user.profile.is_operation_support,
                'is_read_only': user.profile.is_read_only,
            }

        return JsonResponse(data)


class UserPermissionsAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        user = request.user
        permissions = []

        if hasattr(user, 'profile') and user.profile.user_group:
            permissions = list(
                user.profile.user_group.permissions.values_list('name', flat=True)
            )

        return JsonResponse({'permissions': permissions})
