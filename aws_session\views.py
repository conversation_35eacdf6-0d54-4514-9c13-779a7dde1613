from django.shortcuts import render
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse


# Placeholder views for AWS Session management
class SessionListView(LoginRequiredMixin, TemplateView):
    template_name = 'aws_session/session_list.html'


class SessionCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'aws_session/session_create.html'


class SessionDetailView(LoginRequiredMixin, TemplateView):
    template_name = 'aws_session/session_detail.html'


class SessionEditView(LoginRequiredMixin, TemplateView):
    template_name = 'aws_session/session_edit.html'


class SessionDeleteView(LoginRequiredMixin, TemplateView):
    template_name = 'aws_session/session_delete.html'


class SessionTestView(LoginRequiredMixin, TemplateView):
    template_name = 'aws_session/session_test.html'


class AccountListView(LoginRequiredMixin, TemplateView):
    template_name = 'aws_session/account_list.html'


class AccountImportView(LoginRequiredMixin, TemplateView):
    template_name = 'aws_session/account_import.html'


class AccountDetailView(LoginRequiredMixin, TemplateView):
    template_name = 'aws_session/account_detail.html'


# API Views
class SessionListAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        return JsonResponse({'status': 'not_implemented'})


class SessionTestAPIView(LoginRequiredMixin, TemplateView):
    def post(self, request, pk, *args, **kwargs):
        return JsonResponse({'status': 'not_implemented'})


class AccountListAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        return JsonResponse({'status': 'not_implemented'})
