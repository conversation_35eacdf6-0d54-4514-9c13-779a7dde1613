"""
Management command to create sample jobs for testing
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta

from jobs.models import Job, JobExecution, JobSchedule, TaskResult, CollectionMetrics
from aws_session.models import AWSSession
from inventory.models import AWSAccount


class Command(BaseCommand):
    help = 'Create sample jobs for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing jobs before creating new ones',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing jobs...')
            Job.objects.all().delete()

        self.stdout.write('Creating sample jobs...')

        # Get or create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()

        # Get or create AWS session
        aws_session, created = AWSSession.objects.get_or_create(
            name='Default Session',
            defaults={
                'auth_method': 'profile',
                'profile_name': 'default',
                'is_active': True,
                'created_by': admin_user
            }
        )

        # Create sample jobs
        jobs_data = [
            {
                'name': 'Daily Full Collection',
                'description': 'Complete daily collection of all AWS resources across all accounts',
                'job_type': 'full_collection',
                'resource_types': ['EC2', 'S3', 'LAMBDA', 'EKS', 'ECS', 'ECR'],
                'parallel_accounts': 5,
                'parallel_regions': 3,
                'parallel_resources': 10,
                'timeout_minutes': 120,
                'retry_count': 3
            },
            {
                'name': 'EC2 Instance Discovery',
                'description': 'Focused collection of EC2 instances for capacity planning',
                'job_type': 'specific_resource',
                'resource_types': ['EC2'],
                'parallel_accounts': 3,
                'parallel_regions': 2,
                'parallel_resources': 5,
                'timeout_minutes': 60,
                'retry_count': 2
            },
            {
                'name': 'S3 Security Audit',
                'description': 'Collection of S3 buckets for security compliance review',
                'job_type': 'specific_resource',
                'resource_types': ['S3'],
                'parallel_accounts': 2,
                'parallel_regions': 1,
                'parallel_resources': 3,
                'timeout_minutes': 45,
                'retry_count': 3
            },
            {
                'name': 'Container Services Inventory',
                'description': 'Collection of EKS, ECS, and ECR resources',
                'job_type': 'specific_resource',
                'resource_types': ['EKS', 'ECS', 'ECR'],
                'parallel_accounts': 3,
                'parallel_regions': 2,
                'parallel_resources': 6,
                'timeout_minutes': 90,
                'retry_count': 2
            },
            {
                'name': 'Incremental Update',
                'description': 'Quick incremental update of recently changed resources',
                'job_type': 'incremental_collection',
                'resource_types': ['EC2', 'S3', 'LAMBDA'],
                'parallel_accounts': 8,
                'parallel_regions': 4,
                'parallel_resources': 15,
                'timeout_minutes': 30,
                'retry_count': 1
            }
        ]

        created_jobs = []
        for job_data in jobs_data:
            job = Job.objects.create(
                name=job_data['name'],
                description=job_data['description'],
                job_type=job_data['job_type'],
                resource_types=job_data['resource_types'],
                aws_session=aws_session,
                parallel_accounts=job_data['parallel_accounts'],
                parallel_regions=job_data['parallel_regions'],
                parallel_resources=job_data['parallel_resources'],
                timeout_minutes=job_data['timeout_minutes'],
                retry_count=job_data['retry_count'],
                created_by=admin_user,
                is_active=True
            )
            created_jobs.append(job)
            self.stdout.write(f'Created job: {job.name}')

        # Create some sample job executions
        self.stdout.write('Creating sample job executions...')
        
        for i, job in enumerate(created_jobs[:3]):  # Create executions for first 3 jobs
            # Create a completed execution
            execution = JobExecution.objects.create(
                job=job,
                status='completed',
                started_by=admin_user,
                started_at=timezone.now() - timedelta(hours=2),
                completed_at=timezone.now() - timedelta(hours=1, minutes=30),
                total_tasks=20,
                completed_tasks=20,
                failed_tasks=0,
                progress_percentage=100.0,
                resources_collected=150 + (i * 50),
                resources_updated=140 + (i * 45),
                resources_failed=2
            )

            # Create sample task results
            for j in range(5):
                TaskResult.objects.create(
                    job_execution=execution,
                    task_type='resource_collection',
                    task_name=f'Collect {job.resource_types[0]} resources',
                    status='completed',
                    aws_account_id=f'***********{j}',
                    region='us-east-1',
                    resource_type=job.resource_types[0],
                    started_at=execution.started_at + timedelta(minutes=j*5),
                    completed_at=execution.started_at + timedelta(minutes=j*5+3),
                    execution_time=180.5,
                    items_processed=30 + j*5,
                    items_successful=28 + j*5,
                    items_failed=2
                )

            # Create metrics
            CollectionMetrics.objects.create(
                job_execution=execution,
                total_execution_time=1800.0,  # 30 minutes
                average_task_time=180.0,
                total_api_calls=100 + i*20,
                successful_api_calls=95 + i*18,
                failed_api_calls=5 + i*2,
                total_resources_found=execution.resources_collected,
                new_resources_added=50 + i*10,
                existing_resources_updated=execution.resources_updated,
                workers_used=job.parallel_accounts
            )

            self.stdout.write(f'Created execution for job: {job.name}')

        # Create a running execution
        running_job = created_jobs[3]
        running_execution = JobExecution.objects.create(
            job=running_job,
            status='running',
            started_by=admin_user,
            started_at=timezone.now() - timedelta(minutes=15),
            total_tasks=15,
            completed_tasks=8,
            failed_tasks=1,
            progress_percentage=53.3,
            resources_collected=75,
            resources_updated=70,
            resources_failed=5
        )

        # Create some task results for running execution
        for j in range(8):
            TaskResult.objects.create(
                job_execution=running_execution,
                task_type='resource_collection',
                task_name=f'Collect {running_job.resource_types[j % len(running_job.resource_types)]} resources',
                status='completed',
                aws_account_id=f'***********{j}',
                region='us-east-1',
                resource_type=running_job.resource_types[j % len(running_job.resource_types)],
                started_at=running_execution.started_at + timedelta(minutes=j*2),
                completed_at=running_execution.started_at + timedelta(minutes=j*2+1.5),
                execution_time=90.0,
                items_processed=15,
                items_successful=14,
                items_failed=1
            )

        self.stdout.write('Created running execution')

        # Create job schedules for some jobs
        self.stdout.write('Creating job schedules...')
        
        # Daily schedule for full collection
        JobSchedule.objects.create(
            job=created_jobs[0],
            schedule_type='daily',
            start_date=timezone.now().replace(hour=2, minute=0, second=0, microsecond=0),
            is_enabled=True,
            next_run=timezone.now().replace(hour=2, minute=0, second=0, microsecond=0) + timedelta(days=1),
            last_run=timezone.now().replace(hour=2, minute=0, second=0, microsecond=0) - timedelta(days=1)
        )

        # Hourly schedule for incremental updates
        JobSchedule.objects.create(
            job=created_jobs[4],
            schedule_type='hourly',
            start_date=timezone.now().replace(minute=0, second=0, microsecond=0),
            is_enabled=True,
            next_run=timezone.now().replace(minute=0, second=0, microsecond=0) + timedelta(hours=1),
            last_run=timezone.now().replace(minute=0, second=0, microsecond=0) - timedelta(hours=1)
        )

        self.stdout.write(self.style.SUCCESS(f'Successfully created {len(created_jobs)} sample jobs with executions and schedules'))
        self.stdout.write('You can now test the jobs functionality in the web interface!')
